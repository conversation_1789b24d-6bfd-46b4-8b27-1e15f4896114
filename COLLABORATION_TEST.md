# 协作画板测试指南

## 当前状态
✅ 服务器已启动 (端口 4000)
✅ 前端应用已启动 (端口 8082)
✅ 日志刷屏问题已修复
✅ 添加了详细的调试日志

## 测试步骤

### 1. 打开多个浏览器窗口
- 打开第一个窗口：http://localhost:8082/board/0d3bea9f-45a2-4505-9c88-af7ce568b8fe
- 打开第二个窗口：http://localhost:8082/board/0d3bea9f-45a2-4505-9c88-af7ce568b8fe
- 确保两个窗口都显示画板界面

### 2. 测试形状同步
在第一个窗口中：
1. 点击矩形按钮 (□) - 应该立即在画布上添加一个矩形
2. 点击圆形按钮 (○) - 应该立即在画布上添加一个圆形

观察第二个窗口：
- 应该能看到第一个窗口添加的形状实时出现

### 3. 测试绘制同步
在第一个窗口中：
1. 点击画笔按钮 (✏️) 激活绘制模式
2. 在画布上绘制线条或图案

观察第二个窗口：
- 应该能看到绘制的内容实时同步

### 4. 检查调试日志
打开浏览器开发者工具 (F12)，查看控制台：

**预期的日志输出：**
```
[canvas] 工具切换: rectangle
[canvas] 添加矩形
[canvas] object:added 事件触发, isDrawingMode: false, isRemoteUpdate: false, object type: rect
[canvas] 检测到形状添加，触发广播
[canvas] debouncedBroadcast 被调用
[canvas] 本地变更，广播画布状态，对象数量: 1
```

**在另一个窗口中应该看到：**
```
[socket] 收到远端画布状态
[canvas] 已应用远端画布状态
```

## 故障排除

### 如果没有看到实时同步：

1. **检查网络连接**
   - 确保两个窗口都显示了 socket 连接成功的日志
   - 查看服务器日志确认有多个用户连接

2. **检查事件触发**
   - 在控制台中查看是否有 `object:added` 或 `path:created` 事件日志
   - 确认 `debouncedBroadcast` 被调用

3. **检查服务器广播**
   - 查看服务器终端，应该看到广播日志（每5秒最多一次）

4. **重新加载页面**
   - 刷新两个浏览器窗口
   - 重新测试功能

### 常见问题

**Q: 点击形状按钮没有反应**
A: 检查控制台是否有错误，确保 Fabric.js 正确加载

**Q: 绘制没有同步**
A: 确保点击了画笔工具激活绘制模式，查看 `isDrawingMode` 的值

**Q: 服务器没有广播日志**
A: 检查客户端是否正确发送了 `board-op` 事件

## 调试命令

如果需要重新启动服务器：
```bash
# 停止当前服务器 (Ctrl+C)
# 重新启动
node server/index.cjs
```

如果需要查看详细的服务器日志，可以修改服务器代码移除5秒限制：
```javascript
// 在 server/index.cjs 中注释掉时间检查
// if (now - lastTime > 5000) {
  console.log(`[server] ${socket.id} 广播画布状态到房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
// }
```
