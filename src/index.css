@tailwind base;
@tailwind components;
@tailwind utilities;

/* Canvas Drawing App Design System - Beautiful colors and artistic theme */

@layer base {
  :root {
    /* Main canvas colors */
    --background: 252 100% 99%;
    --foreground: 260 15% 15%;

    --card: 0 0% 100%;
    --card-foreground: 260 15% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 260 15% 15%;

    /* Creative primary - vibrant purple */
    --primary: 260 100% 60%;
    --primary-foreground: 0 0% 100%;

    /* Canvas secondary - soft lavender */
    --secondary: 260 30% 96%;
    --secondary-foreground: 260 15% 25%;

    --muted: 260 20% 96%;
    --muted-foreground: 260 10% 50%;

    /* Accent - creative orange */
    --accent: 25 95% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 260 15% 90%;
    --input: 260 15% 92%;
    --ring: 260 100% 60%;

    --radius: 0.75rem;

    /* Canvas-specific colors */
    --canvas-bg: 0 0% 100%;
    --canvas-grid: 260 15% 95%;
    --tool-active: 260 100% 60%;
    --tool-hover: 260 100% 70%;
    
    /* Artistic gradients */
    --gradient-primary: linear-gradient(135deg, hsl(260 100% 60%), hsl(280 100% 70%));
    --gradient-canvas: linear-gradient(180deg, hsl(252 100% 99%), hsl(260 30% 98%));
    --gradient-creative: linear-gradient(45deg, hsl(260 100% 60%), hsl(25 95% 60%), hsl(200 100% 60%));
    
    /* Shadows */
    --shadow-canvas: 0 4px 20px hsl(260 100% 60% / 0.1);
    --shadow-tool: 0 2px 10px hsl(260 100% 60% / 0.15);

    --sidebar-background: 260 30% 98%;
    --sidebar-foreground: 260 15% 25%;
    --sidebar-primary: 260 100% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 260 20% 95%;
    --sidebar-accent-foreground: 260 15% 25%;
    --sidebar-border: 260 15% 90%;
    --sidebar-ring: 260 100% 60%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Canvas-specific utility classes */
@layer components {
  .canvas-surface {
    background: hsl(var(--canvas-bg));
    box-shadow: var(--shadow-canvas);
  }
  
  .tool-button {
    @apply relative p-3 rounded-lg border transition-all duration-200;
    @apply hover:bg-secondary hover:border-primary/50;
    @apply data-[active="true"]:bg-primary data-[active="true"]:text-primary-foreground;
    @apply data-[active="true"]:shadow-lg;
  }
  
  .gradient-bg {
    background: var(--gradient-primary);
  }
  
  .canvas-grid {
    background-image: 
      radial-gradient(circle, hsl(var(--canvas-grid)) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  
  .artistic-gradient {
    background: var(--gradient-creative);
    background-size: 400% 400%;
    animation: gradient-shift 8s ease infinite;
  }
  
  .floating-action {
    @apply fixed bottom-6 right-6 z-50;
    @apply w-14 h-14 rounded-full;
    @apply bg-primary text-primary-foreground;
    @apply shadow-lg hover:shadow-xl;
    @apply transition-all duration-300;
    @apply hover:scale-110;
  }
}

@layer utilities {
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}