import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { Upload, Globe, Lock } from 'lucide-react';

interface PublishDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  boardId: string;
  boardTitle: string;
  boardDescription?: string;
  onPublishSuccess?: () => void;
}

export const PublishDialog = ({ 
  open, 
  onOpenChange, 
  boardId, 
  boardTitle, 
  boardDescription = '',
  onPublishSuccess 
}: PublishDialogProps) => {
  const { user } = useAuth();
  const [title, setTitle] = useState(boardTitle);
  const [description, setDescription] = useState(boardDescription);
  const [isPublic, setIsPublic] = useState(true);
  const [loading, setLoading] = useState(false);

  const handlePublish = async () => {
    if (!title.trim()) {
      toast.error('请输入作品标题');
      return;
    }

    if (!description.trim()) {
      toast.error('请输入作品描述');
      return;
    }

    try {
      setLoading(true);

      // First, check if already published
      const { data: existingPost } = await supabase
        .from('canvas_market_posts')
        .select('id')
        .eq('board_id', boardId)
        .single();

      if (existingPost) {
        toast.error('该画板已经发布到市场');
        return;
      }

      // Update board to be published
      const { error: boardError } = await supabase
        .from('canvas_boards')
        .update({
          is_published: true,
          is_public: isPublic,
          title: title.trim(),
          description: description.trim()
        })
        .eq('id', boardId);

      if (boardError) throw boardError;

      // Create market post
      const { error: postError } = await supabase
        .from('canvas_market_posts')
        .insert({
          board_id: boardId,
          title: title.trim(),
          description: description.trim(),
          likes_count: 0,
          views_count: 0
        });

      if (postError) throw postError;

      toast.success('作品发布成功！');
      onOpenChange(false);
      onPublishSuccess?.();
      
    } catch (error) {
      console.error('Error publishing board:', error);
      toast.error('发布失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            发布到涂鸦市场
          </DialogTitle>
          <DialogDescription>
            将你的画板作品分享给更多人
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">作品标题</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="为你的作品起个好听的名字"
              maxLength={100}
            />
            <div className="text-xs text-muted-foreground text-right">
              {title.length}/100
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">作品描述</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="介绍一下你的创作灵感和作品特色..."
              rows={4}
              maxLength={500}
            />
            <div className="text-xs text-muted-foreground text-right">
              {description.length}/500
            </div>
          </div>

          {/* Public/Private */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-3">
              {isPublic ? (
                <Globe className="w-5 h-5 text-green-600" />
              ) : (
                <Lock className="w-5 h-5 text-orange-600" />
              )}
              <div>
                <div className="font-medium">
                  {isPublic ? '公开作品' : '私密作品'}
                </div>
                <div className="text-sm text-muted-foreground">
                  {isPublic 
                    ? '所有人都可以查看和分享' 
                    : '只有你和协作者可以查看'
                  }
                </div>
              </div>
            </div>
            <Switch
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>

          {/* Publish Button */}
          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handlePublish}
              disabled={loading || !title.trim() || !description.trim()}
              className="flex-1"
            >
              {loading ? '发布中...' : '发布作品'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};