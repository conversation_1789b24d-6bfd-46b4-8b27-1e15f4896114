import { useState } from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Users, Copy, Plus, Trash2, Mail } from 'lucide-react';

interface Collaborator {
  id: string;
  user_id: string;
  role: string;
  joined_at: string;
  last_active: string;
}

interface ShareDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  boardId: string;
  boardTitle: string;
  isOwner: boolean;
  collaborators: Collaborator[];
  onCollaboratorsUpdate: () => void;
}

export const ShareDialog = ({ 
  open, 
  onOpenChange, 
  boardId, 
  boardTitle, 
  isOwner,
  collaborators,
  onCollaboratorsUpdate 
}: ShareDialogProps) => {
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'viewer' | 'editor'>('editor');
  const [loading, setLoading] = useState(false);
  const [shareLink, setShareLink] = useState('');

  const generateShareLink = async () => {
    try {
      setLoading(true);
      
      // Generate a unique share code
      const shareCode = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      
      // Create share link in database
      const { data, error } = await supabase
        .from('canvas_board_shares')
        .insert({
          board_id: boardId,
          share_code: shareCode,
          created_by: (await supabase.auth.getUser()).data.user?.id,
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
          max_uses: 10
        })
        .select()
        .single();

      if (error) throw error;

      const link = `${window.location.origin}/board/join/${shareCode}`;
      setShareLink(link);
      
      // Copy to clipboard
      await navigator.clipboard.writeText(link);
      toast.success('分享链接已复制到剪贴板');
      
    } catch (error) {
      console.error('Error generating share link:', error);
      toast.error('生成分享链接失败');
    } finally {
      setLoading(false);
    }
  };

  const inviteCollaborator = async () => {
    if (!inviteEmail.trim()) {
      toast.error('请输入邮箱地址');
      return;
    }

    try {
      setLoading(true);

      // Find user by email (this would require a profiles table)
      // For now, we'll create a placeholder implementation
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('email', inviteEmail.trim())
        .single();

      if (userError || !userData) {
        toast.error('用户不存在，请确认邮箱地址');
        return;
      }

      // Check if already a collaborator
      const existingCollaborator = collaborators.find(c => c.user_id === userData.id);
      if (existingCollaborator) {
        toast.error('该用户已经是协作者');
        return;
      }

      // Add collaborator
      const { error } = await supabase
        .from('canvas_board_collaborators')
        .insert({
          board_id: boardId,
          user_id: userData.id,
          role: inviteRole
        });

      if (error) throw error;

      toast.success('成功邀请协作者');
      setInviteEmail('');
      onCollaboratorsUpdate();
      
    } catch (error) {
      console.error('Error inviting collaborator:', error);
      toast.error('邀请协作者失败');
    } finally {
      setLoading(false);
    }
  };

  const removeCollaborator = async (collaboratorId: string) => {
    try {
      const { error } = await supabase
        .from('canvas_board_collaborators')
        .delete()
        .eq('id', collaboratorId);

      if (error) throw error;

      toast.success('移除协作者成功');
      onCollaboratorsUpdate();
      
    } catch (error) {
      console.error('Error removing collaborator:', error);
      toast.error('移除协作者失败');
    }
  };

  const getRoleText = (role: string) => {
    switch (role) {
      case 'viewer': return '查看者';
      case 'editor': return '编辑者';
      default: return role;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            分享协作 - {boardTitle}
          </DialogTitle>
          <DialogDescription>
            邀请其他人协作编辑这个画板
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Generate Share Link */}
          <div className="space-y-2">
            <Label>快速分享链接</Label>
            <div className="flex gap-2">
              <Button 
                onClick={generateShareLink} 
                disabled={loading}
                className="flex-1"
              >
                <Copy className="w-4 h-4 mr-2" />
                生成分享链接
              </Button>
            </div>
            {shareLink && (
              <div className="p-2 bg-muted rounded text-sm font-mono break-all">
                {shareLink}
              </div>
            )}
          </div>

          {/* Invite by Email */}
          {isOwner && (
            <div className="space-y-2">
              <Label>邀请协作者</Label>
              <div className="space-y-2">
                <Input
                  type="email"
                  placeholder="输入邮箱地址"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                />
                <div className="flex gap-2">
                  <Select value={inviteRole} onValueChange={(value: any) => setInviteRole(value)}>
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="viewer">查看者</SelectItem>
                      <SelectItem value="editor">编辑者</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button onClick={inviteCollaborator} disabled={loading}>
                    <Plus className="w-4 h-4 mr-2" />
                    邀请
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Current Collaborators */}
          {collaborators.length > 0 && (
            <div className="space-y-2">
              <Label>当前协作者</Label>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {collaborators.map((collaborator) => (
                  <div key={collaborator.id} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4" />
                      <span className="text-sm">{collaborator.user_id}</span>
                      <Badge variant="secondary" className="text-xs">
                        {getRoleText(collaborator.role)}
                      </Badge>
                    </div>
                    {isOwner && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeCollaborator(collaborator.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};