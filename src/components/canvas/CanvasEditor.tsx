import { useEffect, useRef, useState } from "react";
import { io, Socket } from "socket.io-client";
import { Canvas as FabricCanvas, Circle, Rect, PencilBrush } from "fabric";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Pa<PERSON>, Square, Circle as CircleI<PERSON>, <PERSON><PERSON><PERSON>, MousePointer, Trash2 } from "lucide-react";

interface CanvasEditorProps {
  canvasData?: any;
  onSave?: (data: any) => void;
}

// 你可以根据实际路由传递的 boardId 进行参数传递，这里假设通过 window.location.pathname 获取
const getBoardId = () => {
  // 你的路由一般是 /board/:id
  const match = window.location.pathname.match(/\/board\/(\w[\w-]*)/);
  return match ? match[1] : undefined;
};

export const CanvasEditor = ({ canvasData, onSave }: CanvasEditorProps) => {
  // ...
  const isRemoteUpdate = useRef(false); // 远端同步标志
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<FabricCanvas | null>(null);
  const [activeColor, setActiveColor] = useState("#000000");
  const [activeTool, setActiveTool] = useState<"select" | "draw" | "rectangle" | "circle">("select");
  // socket 连接实例
  const socketRef = useRef<Socket | null>(null);
  const boardId = getBoardId();

  useEffect(() => {
    if (!canvasRef.current) return;

    // 初始化 socket 连接（只初始化一次）
    if (!socketRef.current && boardId) {
      console.log('[socket] 尝试连接 socket.io 服务端...');
      socketRef.current = io("http://localhost:4000");
      socketRef.current.on("connect", () => {
        console.log(`[socket] 已连接, socket.id=${socketRef.current?.id}`);
        socketRef.current?.emit("join-board", boardId);
        console.log(`[socket] join-board 房间: ${boardId}`);
      });
      socketRef.current.on("disconnect", () => {
        console.log('[socket] 断开连接');
      });
    }

    const canvas = new FabricCanvas(canvasRef.current, {
      width: 800,
      height: 600,
      backgroundColor: "#ffffff",
    });

    // Manually create and assign the freeDrawingBrush for Fabric.js v6
    const brush = new PencilBrush(canvas);
    brush.color = activeColor;
    brush.width = 5;
    canvas.freeDrawingBrush = brush;
    
    console.log("Initial canvas setup");
    console.log("Canvas created:", canvas);
    console.log("Manual brush created:", brush);
    console.log("freeDrawingBrush assigned:", canvas.freeDrawingBrush);
    
    // Now set drawing mode to false initially
    canvas.isDrawingMode = false;

    // Load existing canvas data if available
    if (canvasData?.strokes) {
      try {
        canvas.loadFromJSON(canvasData.strokes, () => {
          canvas.renderAll();
        });
      } catch (error) {
        console.error("Error loading canvas data:", error);
      }
    }

    setFabricCanvas(canvas);
    toast("Canvas ready! Start drawing!");

    return () => {
      canvas.dispose();
      // 离开房间和断开 socket
      if (socketRef.current && boardId) {
        console.log(`[socket] 离开房间: ${boardId} 并断开连接`);
        socketRef.current.emit("leave-board", boardId);
        socketRef.current.disconnect();
        socketRef.current = null;
      }
    };
  }, [canvasData, boardId]);

  useEffect(() => {
    if (!fabricCanvas) return;

    // 每次 fabricCanvas 变动时，注册 socket 事件，确保作用于最新 canvas
    const socket = socketRef.current;
    if (socket) {
      // 先解绑，防止重复绑定
      socket.off("board-op");
      socket.off("board-ops-sync");
      socket.on("board-op", (canvasState) => {
        console.log('[socket] 收到远端画布状态');
        if (fabricCanvas && canvasState) {
          isRemoteUpdate.current = true;

          // 暂时使用全量同步，但只在没有本地绘制时更新
          if (!fabricCanvas.isDrawingMode || fabricCanvas.getObjects().length === 0) {
            fabricCanvas.loadFromJSON(canvasState, () => {
              fabricCanvas.renderAll();
              isRemoteUpdate.current = false;
              console.log('[canvas] 已应用远端画布状态');
            });
          } else {
            isRemoteUpdate.current = false;
          }
        }
      });
      socket.on("board-ops-sync", (canvasState) => {
        console.log('[socket] 首次同步 board-ops-sync');
        if (fabricCanvas && canvasState) {
          isRemoteUpdate.current = true;
          fabricCanvas.clear();
          fabricCanvas.backgroundColor = "#ffffff";
          fabricCanvas.loadFromJSON(canvasState, () => {
            fabricCanvas.renderAll();
            isRemoteUpdate.current = false;
            console.log('[canvas] 已应用远端初始同步，本地画布刷新');
          });
        }
      });
    }

    console.log("Setting drawing mode:", activeTool === "draw");
    console.log("Active tool:", activeTool);

    fabricCanvas.isDrawingMode = activeTool === "draw";
    
    // Ensure freeDrawingBrush exists and configure it properly
    if (fabricCanvas.freeDrawingBrush) {
      fabricCanvas.freeDrawingBrush.color = activeColor;
      fabricCanvas.freeDrawingBrush.width = 5;
      console.log("Brush configured:", {
        color: fabricCanvas.freeDrawingBrush.color,
        width: fabricCanvas.freeDrawingBrush.width,
        isDrawingMode: fabricCanvas.isDrawingMode
      });
    } else {
      // If brush doesn't exist, create it manually
      const brush = new PencilBrush(fabricCanvas);
      brush.color = activeColor;
      brush.width = 5;
      fabricCanvas.freeDrawingBrush = brush;
      console.log("Created new brush:", brush);
    }

    // Force re-render
    fabricCanvas.renderAll();
  }, [activeTool, activeColor, fabricCanvas]);

  // 监听本地画布变更，实时广播给协作者
  useEffect(() => {
    if (!fabricCanvas || !socketRef.current || !boardId) return;

    // 防抖函数，避免频繁广播
    let broadcastTimeout: NodeJS.Timeout | null = null;
    let stateUpdateTimeout: NodeJS.Timeout | null = null;

    const debouncedBroadcast = () => {
      if (broadcastTimeout) {
        clearTimeout(broadcastTimeout);
      }
      broadcastTimeout = setTimeout(() => {
        if (isRemoteUpdate.current) return; // 远端同步时不广播

        // 发送完整画布状态
        const canvasState = fabricCanvas.toJSON();
        console.log('[canvas] 本地变更，广播画布状态');
        socketRef.current?.emit("board-op", { boardId, canvasState });

        // 同时更新服务器缓存
        if (stateUpdateTimeout) {
          clearTimeout(stateUpdateTimeout);
        }
        stateUpdateTimeout = setTimeout(() => {
          if (!isRemoteUpdate.current) {
            socketRef.current?.emit("board-state-update", { boardId, canvasState });
          }
        }, 500); // 500ms后发送状态更新
      }, 200); // 200ms 防抖延迟
    };

    // 监听绘制完成事件
    const onPathCreated = () => {
      if (isRemoteUpdate.current) return;
      debouncedBroadcast();
    };

    // 监听对象添加（形状）
    const onObjectAdded = () => {
      // 只有在非绘制模式下才响应，避免绘制过程中的频繁触发
      if (!fabricCanvas.isDrawingMode && !isRemoteUpdate.current) {
        debouncedBroadcast();
      }
    };

    // 监听对象修改
    const onObjectModified = () => {
      if (isRemoteUpdate.current) return;
      debouncedBroadcast();
    };

    // 监听对象删除
    const onObjectRemoved = () => {
      if (isRemoteUpdate.current) return;
      debouncedBroadcast();
    };

    // 绑定事件
    fabricCanvas.on("path:created", onPathCreated);
    fabricCanvas.on("object:added", onObjectAdded);
    fabricCanvas.on("object:modified", onObjectModified);
    fabricCanvas.on("object:removed", onObjectRemoved);

    return () => {
      if (broadcastTimeout) {
        clearTimeout(broadcastTimeout);
      }
      if (stateUpdateTimeout) {
        clearTimeout(stateUpdateTimeout);
      }
      fabricCanvas.off("path:created", onPathCreated);
      fabricCanvas.off("object:added", onObjectAdded);
      fabricCanvas.off("object:modified", onObjectModified);
      fabricCanvas.off("object:removed", onObjectRemoved);
    };
  }, [fabricCanvas, boardId]);

  const handleToolClick = (tool: typeof activeTool) => {
    setActiveTool(tool);

    if (tool === "rectangle") {
      const rect = new Rect({
        left: 100,
        top: 100,
        fill: activeColor,
        width: 100,
        height: 100,
      });
      fabricCanvas?.add(rect);
    } else if (tool === "circle") {
      const circle = new Circle({
        left: 100,
        top: 100,
        fill: activeColor,
        radius: 50,
      });
      fabricCanvas?.add(circle);
    }
  };

  const handleClear = () => {
    if (!fabricCanvas) return;
    fabricCanvas.clear();
    fabricCanvas.backgroundColor = "#ffffff";
    fabricCanvas.renderAll();
    toast("Canvas cleared!");
  };

  const handleSave = () => {
    if (!fabricCanvas || !onSave) return;
    
    const canvasJson = fabricCanvas.toJSON();
    onSave({
      strokes: canvasJson,
      settings: {
        width: fabricCanvas.width,
        height: fabricCanvas.height,
        backgroundColor: fabricCanvas.backgroundColor
      }
    });
    toast("Canvas saved!");
  };

  const colors = [
    "#000000", "#ff0000", "#00ff00", "#0000ff", 
    "#ffff00", "#ff00ff", "#00ffff", "#ffa500"
  ];

  return (
    <div className="flex flex-col gap-4">
      {/* Toolbar */}
      <div className="flex gap-2 items-center p-4 bg-card rounded-lg border">
        <Button
          variant={activeTool === "select" ? "default" : "outline"}
          size="sm"
          onClick={() => handleToolClick("select")}
        >
          <MousePointer className="w-4 h-4" />
        </Button>
        
        <Button
          variant={activeTool === "draw" ? "default" : "outline"}
          size="sm"
          onClick={() => handleToolClick("draw")}
        >
          <Pencil className="w-4 h-4" />
        </Button>
        
        <Button
          variant={activeTool === "rectangle" ? "default" : "outline"}
          size="sm"
          onClick={() => handleToolClick("rectangle")}
        >
          <Square className="w-4 h-4" />
        </Button>
        
        <Button
          variant={activeTool === "circle" ? "default" : "outline"}
          size="sm"
          onClick={() => handleToolClick("circle")}
        >
          <CircleIcon className="w-4 h-4" />
        </Button>

        <div className="flex items-center gap-2 ml-4">
          <Palette className="w-4 h-4" />
          <div className="flex gap-1">
            {colors.map((color) => (
              <button
                key={color}
                className={`w-6 h-6 rounded border-2 ${
                  activeColor === color ? "border-foreground" : "border-border"
                }`}
                style={{ backgroundColor: color }}
                onClick={() => setActiveColor(color)}
              />
            ))}
          </div>
        </div>

        <div className="flex gap-2 ml-auto">
          <Button variant="outline" size="sm" onClick={handleClear}>
            <Trash2 className="w-4 h-4" />
          </Button>
          
          {onSave && (
            <Button size="sm" onClick={handleSave}>
              Save
            </Button>
          )}
        </div>
      </div>

      {/* Canvas */}
      <div className="border border-border rounded-lg shadow-lg overflow-hidden bg-background">
        <canvas ref={canvasRef} className="max-w-full" />
      </div>
    </div>
  );
};