import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Palette, Globe, Lock, Users } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

const NewBoard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    is_public: false,
    canvas_width: 800,
    canvas_height: 600,
  });

  const presetSizes = [
    { name: '标准画板', width: 800, height: 600 },
    { name: '正方形', width: 800, height: 800 },
    { name: '横版海报', width: 1200, height: 800 },
    { name: '竖版海报', width: 800, height: 1200 },
    { name: 'A4 横向', width: 1190, height: 842 },
    { name: 'A4 竖向', width: 842, height: 1190 },
    { name: '自定义', width: 0, height: 0 },
  ];

  const handlePresetChange = (presetName: string) => {
    const preset = presetSizes.find(p => p.name === presetName);
    if (preset && preset.name !== '自定义') {
      setFormData({
        ...formData,
        canvas_width: preset.width,
        canvas_height: preset.height,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast({
        title: "请输入画板标题",
        variant: "destructive",
      });
      return;
    }

    if (formData.canvas_width < 100 || formData.canvas_height < 100) {
      toast({
        title: "画布尺寸不能小于 100px",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);

    try {
      const boardData = {
        user_id: user?.id,
        title: formData.title.trim(),
        description: formData.description.trim() || null,
        is_public: formData.is_public,
        settings: {
          width: formData.canvas_width,
          height: formData.canvas_height,
          backgroundColor: '#ffffff'
        },
        canvas_data: {
          strokes: [],
          settings: {}
        }
      };

      const { data, error } = await supabase
        .from('canvas_boards')
        .insert(boardData)
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "画板创建成功！",
        description: "开始您的创作之旅",
      });

      // Navigate to the new board
      navigate(`/board/${data.id}`);
    } catch (error) {
      console.error('Error creating board:', error);
      toast({
        title: "创建失败",
        description: "无法创建画板，请重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="p-6 max-w-2xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">新建画板</h1>
            <p className="text-muted-foreground mt-1">
              创建一个全新的创作空间
            </p>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="w-5 h-5" />
                基本信息
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">画板标题 *</Label>
                <Input
                  id="title"
                  placeholder="为您的画板起个名字..."
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">描述 (可选)</Label>
                <Textarea
                  id="description"
                  placeholder="描述这个画板的用途或主题..."
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>画布设置</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>预设尺寸</Label>
                <Select onValueChange={handlePresetChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择预设尺寸" />
                  </SelectTrigger>
                  <SelectContent>
                    {presetSizes.map((preset) => (
                      <SelectItem key={preset.name} value={preset.name}>
                        {preset.name}
                        {preset.width > 0 && ` (${preset.width} x ${preset.height})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="width">宽度 (px)</Label>
                  <Input
                    id="width"
                    type="number"
                    min="100"
                    max="2000"
                    value={formData.canvas_width}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      canvas_width: parseInt(e.target.value) || 800 
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="height">高度 (px)</Label>
                  <Input
                    id="height"
                    type="number"
                    min="100"
                    max="2000"
                    value={formData.canvas_height}
                    onChange={(e) => setFormData({ 
                      ...formData, 
                      canvas_height: parseInt(e.target.value) || 600 
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                隐私设置
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    {formData.is_public ? (
                      <Globe className="w-4 h-4 text-primary" />
                    ) : (
                      <Lock className="w-4 h-4 text-muted-foreground" />
                    )}
                    <Label htmlFor="is_public">
                      {formData.is_public ? '公开画板' : '私人画板'}
                    </Label>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {formData.is_public 
                      ? '其他用户可以查看和发现这个画板' 
                      : '只有您和受邀协作者可以访问'
                    }
                  </p>
                </div>
                <Switch
                  id="is_public"
                  checked={formData.is_public}
                  onCheckedChange={(checked) => 
                    setFormData({ ...formData, is_public: checked })
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate(-1)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="flex-1 gradient-bg text-white"
            >
              {loading ? '创建中...' : '创建画板'}
            </Button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default NewBoard;