import { useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { CanvasEditor } from '@/components/canvas/CanvasEditor';
import { ShareDialog } from '@/components/board/ShareDialog';
import { PublishDialog } from '@/components/board/PublishDialog';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Users, Share, Upload } from 'lucide-react';

interface Board {
  id: string;
  title: string;
  description: string;
  canvas_data: any;
  settings: any;
  is_published: boolean;
  user_id: string;
  canvas_board_collaborators: {
    id: string;
    user_id: string;
    role: string;
    joined_at: string;
    last_active: string;
  }[];
}

const BoardDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const [board, setBoard] = useState<Board | null>(null);
  const [loading, setLoading] = useState(true);
  const [shareDialogOpen, setShareDialogOpen] = useState(false);
  const [publishDialogOpen, setPublishDialogOpen] = useState(false);

  useEffect(() => {
    if (id) {
      fetchBoard();
    }
  }, [id]);

  const fetchBoard = async () => {
    try {
      const { data, error } = await supabase
        .from('canvas_boards')
        .select(`
          *,
          canvas_board_collaborators(
            id,
            user_id,
            role,
            joined_at,
            last_active
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        toast.error('Failed to load board');
        console.error(error);
        return;
      }

      setBoard(data);
    } catch (error) {
      toast.error('Failed to load board');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (canvasData: any) => {
    if (!id) return;

    try {
      const { error } = await supabase
        .from('canvas_boards')
        .update({
          canvas_data: canvasData,
          updated_at: new Date().toISOString()
        })
        .eq('id', id);

      if (error) {
        toast.error('Failed to save board');
        console.error(error);
        return;
      }

      toast.success('Board saved successfully!');
    } catch (error) {
      toast.error('Failed to save board');
      console.error(error);
    }
  };

  const fetchCollaborators = async () => {
    if (!id) return;
    
    try {
      const { data, error } = await supabase
        .from('canvas_boards')
        .select(`
          canvas_board_collaborators(
            id,
            user_id,
            role,
            joined_at,
            last_active
          )
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      setBoard(prev => prev ? {
        ...prev,
        canvas_board_collaborators: data.canvas_board_collaborators || []
      } : null);
    } catch (error) {
      console.error('Error fetching collaborators:', error);
    }
  };

  const isOwner = board?.user_id === user?.id;

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!board) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <h1 className="text-2xl font-bold text-destructive">Board not found</h1>
          <p className="text-muted-foreground">The board you're looking for doesn't exist or you don't have access to it.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">{board.title}</h1>
            {board.description && (
              <p className="text-muted-foreground">{board.description}</p>
            )}
            {board.canvas_board_collaborators.length > 0 && (
              <div className="flex items-center gap-2 mt-2">
                <Users className="w-4 h-4" />
                <span className="text-sm text-muted-foreground">
                  {board.canvas_board_collaborators.length} 个协作者
                </span>
              </div>
            )}
          </div>
          
          <div className="flex gap-2">
            {!board.is_published && isOwner && (
              <Button
                onClick={() => setPublishDialogOpen(true)}
              >
                <Upload className="w-4 h-4 mr-2" />
                发布到市场
              </Button>
            )}
            
            <Button
              variant="outline"
              onClick={() => setShareDialogOpen(true)}
            >
              <Share className="w-4 h-4 mr-2" />
              分享协作
            </Button>
          </div>
        </div>
        
        <CanvasEditor 
          canvasData={board.canvas_data} 
          onSave={handleSave}
        />

        <PublishDialog
          open={publishDialogOpen}
          onOpenChange={setPublishDialogOpen}
          boardId={board.id}
          boardTitle={board.title}
          boardDescription={board.description}
          onPublishSuccess={fetchBoard}
        />

        <ShareDialog
          open={shareDialogOpen}
          onOpenChange={setShareDialogOpen}
          boardId={board.id}
          boardTitle={board.title}
          isOwner={isOwner}
          collaborators={board.canvas_board_collaborators}
          onCollaboratorsUpdate={fetchCollaborators}
        />
      </div>
    </DashboardLayout>
  );
};

export default BoardDetail;