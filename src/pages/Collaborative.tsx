import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, Clock, Eye, Plus } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';

interface CollaborativeBoard {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string | null;
  updated_at: string;
  user_id: string;
  canvas_board_collaborators: {
    id: string;
    user_id: string;
    role: string;
    last_active: string;
  }[];
}

const Collaborative = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [boards, setBoards] = useState<CollaborativeBoard[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchCollaborativeBoards();
    }
  }, [user]);

  const fetchCollaborativeBoards = async () => {
    setLoading(true);
    try {
      // 查询自己拥有的所有画板（无论是否有协作者）
      const { data: ownedBoards, error: ownedError } = await supabase
        .from('canvas_boards')
        .select(`*, canvas_board_collaborators(id, user_id, role, last_active)`)
        .eq('user_id', user?.id)
        .order('updated_at', { ascending: false });

      if (ownedError) {
        toast.error('获取自己拥有的画板失败');
        console.error(ownedError);
        setBoards([]);
        setLoading(false);
        return;
      }

      // 查询自己作为协作者参与的所有画板
      const { data: collaboratedBoards, error: collaboratedError } = await supabase
        .from('canvas_boards')
        .select(`*, canvas_board_collaborators(id, user_id, role, last_active)`)
        .filter('canvas_board_collaborators.user_id', 'eq', user?.id)
        .order('updated_at', { ascending: false });

      if (collaboratedError) {
        toast.error('获取协作画板失败');
        console.error(collaboratedError);
        setBoards(ownedBoards || []);
        setLoading(false);
        return;
      }

      // 合并并去重（以 id 为唯一标识）
      const allBoardsMap = new Map();
      (ownedBoards || []).forEach(board => allBoardsMap.set(board.id, board));
      (collaboratedBoards || []).forEach(board => allBoardsMap.set(board.id, board));
      const allBoards = Array.from(allBoardsMap.values());

      setBoards(allBoards);
    } catch (error) {
      toast.error('获取协作画板失败');
      console.error(error);
      setBoards([]);
    } finally {
      setLoading(false);
    }
  };


  const formatLastActive = (date: string) => {
    const now = new Date();
    const lastActive = new Date(date);
    const diffInHours = Math.floor((now.getTime() - lastActive.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return '刚刚活跃';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    return `${Math.floor(diffInHours / 24)}天前`;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold">协作画板</h1>
            <p className="text-muted-foreground">与团队一起创作的画板</p>
          </div>
          
          <Button onClick={() => navigate('/board/new')}>
            <Plus className="w-4 h-4 mr-2" />
            新建画板
          </Button>
        </div>

        {boards.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Users className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-semibold mb-2">暂无协作画板</h3>
              <p className="text-muted-foreground mb-4">
                开始创建画板并邀请其他人协作，或等待其他人邀请你加入协作
              </p>
              <div className="flex gap-2 justify-center">
                <Button onClick={() => navigate('/board/new')}>
                  <Plus className="w-4 h-4 mr-2" />
                  创建新画板
                </Button>
                <Button variant="outline" onClick={() => navigate('/boards')}>
                  <Eye className="w-4 h-4 mr-2" />
                  查看我的画板
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {boards.map((board) => (
              <Card key={board.id} className="hover:shadow-md transition-shadow cursor-pointer">
                <Link to={`/board/${board.id}`}>
                  <CardHeader className="pb-3">
                    {board.thumbnail_url ? (
                      <img
                        src={board.thumbnail_url}
                        alt={board.title}
                        className="w-full h-32 object-cover rounded-md mb-3"
                      />
                    ) : (
                      <div className="w-full h-32 bg-gradient-to-br from-primary/20 to-accent/20 rounded-md mb-3 flex items-center justify-center">
                        <Users className="w-8 h-8 text-muted-foreground" />
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg truncate">{board.title}</CardTitle>
                      <Badge variant="secondary" className="ml-2">
                        <Users className="w-3 h-3 mr-1" />
                        {board.canvas_board_collaborators.length}
                      </Badge>
                    </div>
                    
                    {board.description && (
                      <CardDescription className="line-clamp-2">
                        {board.description}
                      </CardDescription>
                    )}
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="w-3 h-3 mr-1" />
                        更新于 {new Date(board.updated_at).toLocaleDateString()}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="text-xs text-muted-foreground">
                          协作者角色: {board.canvas_board_collaborators.find(c => c.user_id === user?.id)?.role || '所有者'}
                        </div>
                        
                        {board.canvas_board_collaborators[0] && (
                          <div className="text-xs text-muted-foreground">
                            {formatLastActive(board.canvas_board_collaborators[0].last_active)}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Collaborative;