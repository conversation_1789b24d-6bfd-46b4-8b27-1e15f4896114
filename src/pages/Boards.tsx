import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Search, Grid, List, Filter, Users, Lock, Globe } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface Board {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  canvas_board_collaborators?: Array<{ id: string; user_id: string; role: string }>;
}

const Boards = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [boards, setBoards] = useState<Board[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'updated' | 'created' | 'title'>('updated');
  const [filterBy, setFilterBy] = useState<'all' | 'public' | 'private' | 'collaborative'>('all');

  useEffect(() => {
    if (user) {
      fetchBoards();
    }
  }, [user, sortBy]);

  const fetchBoards = async () => {
    try {
      let query = supabase
        .from('canvas_boards')
        .select(`
          *,
          canvas_board_collaborators(id, user_id, role)
        `)
        .eq('user_id', user?.id);

      // Apply sorting
      if (sortBy === 'updated') {
        query = query.order('updated_at', { ascending: false });
      } else if (sortBy === 'created') {
        query = query.order('created_at', { ascending: false });
      } else {
        query = query.order('title', { ascending: true });
      }

      const { data, error } = await query;

      if (error) throw error;
      setBoards(data || []);
    } catch (error) {
      console.error('Error fetching boards:', error);
      toast({
        title: "获取画板失败",
        description: "无法加载您的画板列表",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filteredBoards = boards.filter(board => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!board.title.toLowerCase().includes(query) && 
          !board.description?.toLowerCase().includes(query)) {
        return false;
      }
    }

    // Type filter
    if (filterBy === 'public' && !board.is_public) return false;
    if (filterBy === 'private' && board.is_public) return false;
    if (filterBy === 'collaborative' && 
        (!board.canvas_board_collaborators || board.canvas_board_collaborators.length === 0)) {
      return false;
    }

    return true;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold">我的画板</h1>
            <p className="text-muted-foreground mt-1">
              管理您的所有创作项目
            </p>
          </div>
          <Button asChild size="lg" className="gradient-bg text-white">
            <Link to="/board/new">
              <Plus className="w-5 h-5 mr-2" />
              新建画板
            </Link>
          </Button>
        </div>

        {/* Filters and Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="搜索画板..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={filterBy} onValueChange={(value: any) => setFilterBy(value)}>
            <SelectTrigger className="w-full sm:w-40">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部画板</SelectItem>
              <SelectItem value="public">公开画板</SelectItem>
              <SelectItem value="private">私人画板</SelectItem>
              <SelectItem value="collaborative">协作画板</SelectItem>
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-full sm:w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="updated">最近更新</SelectItem>
              <SelectItem value="created">创建时间</SelectItem>
              <SelectItem value="title">标题</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="icon"
              onClick={() => setViewMode('list')}
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Boards Display */}
        {filteredBoards.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="space-y-4">
              <div className="p-4 rounded-full bg-muted inline-block">
                <div className="w-8 h-8 text-muted-foreground" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">
                  {searchQuery || filterBy !== 'all' ? '未找到匹配的画板' : '还没有画板'}
                </h3>
                <p className="text-muted-foreground">
                  {searchQuery || filterBy !== 'all' 
                    ? '试试调整搜索条件或筛选器'
                    : '创建您的第一个画板开始创作'
                  }
                </p>
              </div>
              {!searchQuery && filterBy === 'all' && (
                <Button asChild className="gradient-bg text-white">
                  <Link to="/board/new">
                    <Plus className="w-4 h-4 mr-2" />
                    创建画板
                  </Link>
                </Button>
              )}
            </div>
          </Card>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredBoards.map((board) => (
              <Card key={board.id} className="group hover:shadow-lg transition-shadow cursor-pointer">
                <Link to={`/board/${board.id}`}>
                  <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-t-lg relative overflow-hidden">
                    {board.thumbnail_url ? (
                      <img 
                        src={board.thumbnail_url} 
                        alt={board.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <div className="w-12 h-12 text-muted-foreground" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2 flex gap-1">
                      {board.is_public ? (
                        <Badge variant="secondary" className="text-xs">
                          <Globe className="w-3 h-3 mr-1" />
                          公开
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="text-xs">
                          <Lock className="w-3 h-3 mr-1" />
                          私人
                        </Badge>
                      )}
                      {board.canvas_board_collaborators && board.canvas_board_collaborators.length > 0 && (
                        <Badge variant="outline" className="text-xs">
                          <Users className="w-3 h-3 mr-1" />
                          {board.canvas_board_collaborators.length + 1}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold group-hover:text-primary transition-colors">
                      {board.title}
                    </h3>
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {board.description || '暂无描述'}
                    </p>
                    <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
                      <span>
                        更新于 {new Date(board.updated_at).toLocaleDateString()}
                      </span>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBoards.map((board) => (
              <Card key={board.id} className="group hover:shadow-lg transition-shadow cursor-pointer">
                <Link to={`/board/${board.id}`}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg flex items-center justify-center">
                        {board.thumbnail_url ? (
                          <img 
                            src={board.thumbnail_url} 
                            alt={board.title}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <div className="w-6 h-6 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold group-hover:text-primary transition-colors">
                          {board.title}
                        </h3>
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-1">
                          {board.description || '暂无描述'}
                        </p>
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>
                            更新于 {new Date(board.updated_at).toLocaleDateString()}
                          </span>
                          <div className="flex gap-2">
                            {board.is_public ? (
                              <Badge variant="secondary" className="text-xs">
                                <Globe className="w-3 h-3 mr-1" />
                                公开
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                <Lock className="w-3 h-3 mr-1" />
                                私人
                              </Badge>
                            )}
                            {board.canvas_board_collaborators && board.canvas_board_collaborators.length > 0 && (
                              <Badge variant="outline" className="text-xs">
                                <Users className="w-3 h-3 mr-1" />
                                协作
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Boards;