import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Heart, Search, Calendar, Trash2, Eye } from 'lucide-react';
import { Link } from 'react-router-dom';

interface FavoritePost {
  id: string;
  created_at: string;
  canvas_market_posts: {
    id: string;
    title: string;
    description: string;
    likes_count: number;
    views_count: number;
    created_at: string;
    canvas_boards: {
      id: string;
      title: string;
      thumbnail_url: string;
    };
  };
}

const Favorites = () => {
  const { user } = useAuth();
  const [favorites, setFavorites] = useState<FavoritePost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    if (user) {
      fetchFavorites();
    }
  }, [user]);

  const fetchFavorites = async () => {
    try {
      const { data, error } = await supabase
        .from('canvas_market_likes')
        .select(`
          id,
          created_at,
          canvas_market_posts!inner(
            id,
            title,
            description,
            likes_count,
            views_count,
            created_at,
            canvas_boards!inner(
              id,
              title,
              thumbnail_url
            )
          )
        `)
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });

      if (error) {
        toast.error('获取收藏失败');
        console.error(error);
        return;
      }

      setFavorites(data || []);
    } catch (error) {
      toast.error('获取收藏失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const removeFavorite = async (likeId: string, postTitle: string) => {
    try {
      const { error } = await supabase
        .from('canvas_market_likes')
        .delete()
        .eq('id', likeId);

      if (error) throw error;

      toast.success(`已从收藏中移除「${postTitle}」`);
      setFavorites(favorites.filter(fav => fav.id !== likeId));
    } catch (error) {
      toast.error('移除收藏失败');
      console.error(error);
    }
  };

  const filteredFavorites = favorites.filter(favorite => {
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      return favorite.canvas_market_posts.title.toLowerCase().includes(query) || 
             favorite.canvas_market_posts.description.toLowerCase().includes(query);
    }
    return true;
  });

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">我的收藏</h1>
              <p className="text-muted-foreground">收藏的精彩作品</p>
            </div>
          </div>

          {/* Search */}
          <div className="relative max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="搜索收藏的作品..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {filteredFavorites.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                <Heart className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {searchQuery ? '未找到匹配的收藏' : '暂无收藏'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery 
                  ? '尝试使用其他关键词搜索' 
                  : '去市场发现更多精彩作品并收藏它们'
                }
              </p>
              {!searchQuery && (
                <Button asChild>
                  <Link to="/market">浏览市场</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="mb-4">
              <p className="text-sm text-muted-foreground">
                共收藏了 {filteredFavorites.length} 个作品
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredFavorites.map((favorite) => {
                const post = favorite.canvas_market_posts;
                return (
                  <Card key={favorite.id} className="group hover:shadow-lg transition-shadow">
                    <Link to={`/board/${post.canvas_boards.id}`}>
                      <div className="aspect-square bg-gradient-to-br from-primary/10 to-accent/10 relative overflow-hidden rounded-t-lg">
                        {post.canvas_boards.thumbnail_url ? (
                          <img
                            src={post.canvas_boards.thumbnail_url}
                            alt={post.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Heart className="w-12 h-12 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg truncate">{post.title}</CardTitle>
                        {post.description && (
                          <CardDescription className="line-clamp-2">
                            {post.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <div className="flex items-center gap-3">
                              <div className="flex items-center gap-1">
                                <Heart className="w-3 h-3" />
                                {post.likes_count}
                              </div>
                              <div className="flex items-center gap-1">
                                <Eye className="w-3 h-3" />
                                {post.views_count}
                              </div>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              {new Date(post.created_at).toLocaleDateString()}
                            </div>
                          </div>
                          
                          <div className="text-xs text-muted-foreground">
                            收藏于 {new Date(favorite.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </CardContent>
                    </Link>
                    
                    <div className="px-4 pb-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.preventDefault();
                          removeFavorite(favorite.id, post.title);
                        }}
                        className="w-full text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        取消收藏
                      </Button>
                    </div>
                  </Card>
                );
              })}
            </div>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Favorites;