import { useEffect, useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Heart, Eye, TrendingUp, Search, Filter, Calendar, Grid, List } from 'lucide-react';
import { Link } from 'react-router-dom';

interface MarketPost {
  id: string;
  board_id: string;
  title: string;
  description: string;
  likes_count: number;
  views_count: number;
  featured: boolean;
  created_at: string;
  updated_at: string;
  canvas_boards: {
    id: string;
    title: string;
    thumbnail_url: string;
  };
}

const Market = () => {
  const { user } = useAuth();
  const [posts, setPosts] = useState<MarketPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'likes' | 'views' | 'recent'>('likes');

  useEffect(() => {
    fetchMarketPosts();
  }, [sortBy]);

  const fetchMarketPosts = async () => {
    try {
      let query = supabase
        .from('canvas_market_posts')
        .select(`
          *,
          canvas_boards!inner(id, title, thumbnail_url)
        `);

      // Apply sorting
      switch (sortBy) {
        case 'likes':
          query = query.order('likes_count', { ascending: false });
          break;
        case 'views':
          query = query.order('views_count', { ascending: false });
          break;
        case 'recent':
          query = query.order('created_at', { ascending: false });
          break;
      }

      const { data, error } = await query;

      if (error) {
        toast.error('获取市场作品失败');
        console.error(error);
        return;
      }

      setPosts(data || []);
    } catch (error) {
      toast.error('获取市场作品失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const filteredPosts = posts.filter(post => {
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      return post.title.toLowerCase().includes(query) || 
             post.description.toLowerCase().includes(query);
    }
    return true;
  });

  const toggleLike = async (postId: string) => {
    if (!user) {
      toast.error('请先登录');
      return;
    }

    try {
      // Check if already liked
      const { data: existingLike } = await supabase
        .from('canvas_market_likes')
        .select('id')
        .eq('post_id', postId)
        .eq('user_id', user.id)
        .single();

      if (existingLike) {
        // Unlike
        const { error } = await supabase
          .from('canvas_market_likes')
          .delete()
          .eq('post_id', postId)
          .eq('user_id', user.id);

        if (error) throw error;
        toast.success('已取消点赞');
      } else {
        // Like
        const { error } = await supabase
          .from('canvas_market_likes')
          .insert({
            post_id: postId,
            user_id: user.id
          });

        if (error) throw error;
        toast.success('点赞成功');
      }

      // Refresh posts to update like count
      fetchMarketPosts();
    } catch (error) {
      toast.error('操作失败');
      console.error(error);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="p-6 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">涂鸦市场</h1>
              <p className="text-muted-foreground">发现和分享创意作品</p>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="搜索作品..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-full sm:w-40">
                <TrendingUp className="w-4 h-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="likes">最多点赞</SelectItem>
                <SelectItem value="views">最多浏览</SelectItem>
                <SelectItem value="recent">最新发布</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {filteredPosts.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
                <Heart className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold mb-2">暂无作品</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery ? '未找到匹配的作品' : '市场中还没有发布的作品'}
              </p>
              <Button asChild>
                <Link to="/boards">去发布作品</Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
              : 'grid-cols-1'
          }`}>
            {filteredPosts.map((post) => (
              <Card key={post.id} className="group hover:shadow-lg transition-shadow">
                <Link to={`/board/${post.canvas_boards.id}`}>
                  {viewMode === 'grid' ? (
                    <>
                      <div className="aspect-square bg-gradient-to-br from-primary/10 to-accent/10 relative overflow-hidden rounded-t-lg">
                        {post.canvas_boards.thumbnail_url ? (
                          <img
                            src={post.canvas_boards.thumbnail_url}
                            alt={post.title}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Heart className="w-12 h-12 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      
                      <CardHeader className="pb-2">
                        <CardTitle className="text-lg truncate">{post.title}</CardTitle>
                        {post.description && (
                          <CardDescription className="line-clamp-2">
                            {post.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-1">
                              <Heart className="w-3 h-3" />
                              {post.likes_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {post.views_count}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(post.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </CardContent>
                    </>
                  ) : (
                    <div className="flex gap-4 p-4">
                      <div className="w-24 h-24 bg-gradient-to-br from-primary/10 to-accent/10 rounded-lg flex-shrink-0 overflow-hidden">
                        {post.canvas_boards.thumbnail_url ? (
                          <img
                            src={post.canvas_boards.thumbnail_url}
                            alt={post.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Heart className="w-6 h-6 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg mb-1 truncate">{post.title}</CardTitle>
                        {post.description && (
                          <CardDescription className="line-clamp-2 mb-2">
                            {post.description}
                          </CardDescription>
                        )}
                        
                        <div className="flex items-center justify-between text-sm text-muted-foreground">
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-1">
                              <Heart className="w-3 h-3" />
                              {post.likes_count}
                            </div>
                            <div className="flex items-center gap-1">
                              <Eye className="w-3 h-3" />
                              {post.views_count}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(post.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </Link>
                
                <div className="px-4 pb-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      toggleLike(post.id);
                    }}
                    className="w-full"
                  >
                    <Heart className="w-4 h-4 mr-2" />
                    点赞
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default Market;