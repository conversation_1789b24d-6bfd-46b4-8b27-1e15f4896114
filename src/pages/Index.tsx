import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Plus, Users, Palette, TrendingUp, <PERSON>, Eye, Calendar } from 'lucide-react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface Board {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  canvas_board_collaborators?: Array<{ id: string; user_id: string; role: string }>;
}

interface MarketPost {
  id: string;
  title: string;
  description: string;
  likes_count: number;
  views_count: number;
  created_at: string;
  canvas_boards: {
    id: string;
    title: string;
    thumbnail_url: string;
  };
}

const Index = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [recentBoards, setRecentBoards] = useState<Board[]>([]);
  const [trendingPosts, setTrendingPosts] = useState<MarketPost[]>([]);
  const [stats, setStats] = useState({
    totalBoards: 0,
    collaborativeBoards: 0,
    totalLikes: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      // Fetch recent boards
      const { data: boards, error: boardsError } = await supabase
        .from('canvas_boards')
        .select(`
          *,
          canvas_board_collaborators(id, user_id, role)
        `)
        .eq('user_id', user?.id)
        .order('updated_at', { ascending: false })
        .limit(6);

      if (boardsError) throw boardsError;
      setRecentBoards(boards || []);

      // Fetch trending market posts
      const { data: posts, error: postsError } = await supabase
        .from('canvas_market_posts')
        .select(`
          *,
          canvas_boards!inner(id, title, thumbnail_url)
        `)
        .order('likes_count', { ascending: false })
        .limit(4);

      if (postsError) throw postsError;
      setTrendingPosts(posts || []);

      // Calculate stats
      const totalBoards = boards?.length || 0;
      const collaborativeBoards = boards?.filter(b => 
        b.canvas_board_collaborators && b.canvas_board_collaborators.length > 0
      ).length || 0;

      setStats({
        totalBoards,
        collaborativeBoards,
        totalLikes: 0 // Will implement likes counting later
      });

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast({
        title: "获取数据失败",
        description: "无法加载仪表板数据",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-6 space-y-6">
        {/* Welcome Section */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">
              欢迎回来，{user?.user_metadata?.full_name || '创作者'}！
            </h1>
            <p className="text-muted-foreground mt-1">
              继续您的创作之旅，与世界分享您的艺术作品
            </p>
          </div>
          <Button asChild size="lg" className="gradient-bg text-white">
            <Link to="/board/new">
              <Plus className="w-5 h-5 mr-2" />
              新建画板
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-primary/10">
                  <Palette className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalBoards}</p>
                  <p className="text-sm text-muted-foreground">我的画板</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-accent/10">
                  <Users className="w-6 h-6 text-accent" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.collaborativeBoards}</p>
                  <p className="text-sm text-muted-foreground">协作画板</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 rounded-full bg-destructive/10">
                  <Heart className="w-6 h-6 text-destructive" />
                </div>
                <div>
                  <p className="text-2xl font-bold">{stats.totalLikes}</p>
                  <p className="text-sm text-muted-foreground">获得点赞</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Boards */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">最近的画板</h2>
            <Button variant="outline" asChild>
              <Link to="/boards">查看全部</Link>
            </Button>
          </div>

          {recentBoards.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="space-y-4">
                <div className="p-4 rounded-full bg-muted inline-block">
                  <Palette className="w-8 h-8 text-muted-foreground" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">开始您的创作之旅</h3>
                  <p className="text-muted-foreground">
                    创建第一个画板，释放您的创造力
                  </p>
                </div>
                <Button asChild className="gradient-bg text-white">
                  <Link to="/board/new">
                    <Plus className="w-4 h-4 mr-2" />
                    创建画板
                  </Link>
                </Button>
              </div>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentBoards.map((board) => (
                <Card key={board.id} className="group hover:shadow-lg transition-shadow cursor-pointer">
                  <Link to={`/board/${board.id}`}>
                    <div className="aspect-video bg-gradient-to-br from-primary/10 to-accent/10 rounded-t-lg relative overflow-hidden">
                      {board.thumbnail_url ? (
                        <img 
                          src={board.thumbnail_url} 
                          alt={board.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <Palette className="w-12 h-12 text-muted-foreground" />
                        </div>
                      )}
                      <div className="absolute top-2 right-2 flex gap-1">
                        {board.is_public && (
                          <Badge variant="secondary" className="text-xs">
                            公开
                          </Badge>
                        )}
                        {board.canvas_board_collaborators && board.canvas_board_collaborators.length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Users className="w-3 h-3 mr-1" />
                            {board.canvas_board_collaborators.length + 1}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <CardContent className="p-4">
                      <h3 className="font-semibold group-hover:text-primary transition-colors">
                        {board.title}
                      </h3>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {board.description || '暂无描述'}
                      </p>
                      <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
                        <span>
                          <Calendar className="w-3 h-3 inline mr-1" />
                          {new Date(board.updated_at).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Trending Market */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">热门作品</h2>
            <Button variant="outline" asChild>
              <Link to="/market">
                <TrendingUp className="w-4 h-4 mr-2" />
                浏览市场
              </Link>
            </Button>
          </div>

          {trendingPosts.length === 0 ? (
            <Card className="p-8 text-center">
              <p className="text-muted-foreground">暂无热门作品</p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {trendingPosts.map((post) => (
                <Card key={post.id} className="group hover:shadow-lg transition-shadow cursor-pointer">
                  <Link to={`/market/${post.id}`}>
                    <div className="aspect-square bg-gradient-to-br from-primary/10 to-accent/10 rounded-t-lg relative overflow-hidden">
                      {post.canvas_boards.thumbnail_url ? (
                        <img 
                          src={post.canvas_boards.thumbnail_url} 
                          alt={post.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <Palette className="w-8 h-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    <CardContent className="p-3">
                      <h3 className="font-medium text-sm group-hover:text-primary transition-colors line-clamp-1">
                        {post.title}
                      </h3>
                      <div className="flex items-center gap-3 mt-2 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <Heart className="w-3 h-3" />
                          {post.likes_count}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {post.views_count}
                        </span>
                      </div>
                    </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Index;
