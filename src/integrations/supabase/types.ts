export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      bookmarks: {
        Row: {
          _id: string
          cover: string | null
          created: string | null
          created_at: string | null
          excerpt: string | null
          id: string
          link: string
          tags: string[] | null
          title: string
          user_id: string | null
        }
        Insert: {
          _id: string
          cover?: string | null
          created?: string | null
          created_at?: string | null
          excerpt?: string | null
          id?: string
          link: string
          tags?: string[] | null
          title: string
          user_id?: string | null
        }
        Update: {
          _id?: string
          cover?: string | null
          created?: string | null
          created_at?: string | null
          excerpt?: string | null
          id?: string
          link?: string
          tags?: string[] | null
          title?: string
          user_id?: string | null
        }
        Relationships: []
      }
      canvas_board_collaborators: {
        Row: {
          board_id: string
          id: string
          joined_at: string | null
          last_active: string | null
          role: string
          user_id: string
        }
        Insert: {
          board_id: string
          id?: string
          joined_at?: string | null
          last_active?: string | null
          role?: string
          user_id: string
        }
        Update: {
          board_id?: string
          id?: string
          joined_at?: string | null
          last_active?: string | null
          role?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "canvas_board_collaborators_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "canvas_boards"
            referencedColumns: ["id"]
          },
        ]
      }
      canvas_board_shares: {
        Row: {
          board_id: string
          created_at: string | null
          created_by: string
          current_uses: number | null
          expires_at: string | null
          id: string
          max_uses: number | null
          share_code: string
        }
        Insert: {
          board_id: string
          created_at?: string | null
          created_by: string
          current_uses?: number | null
          expires_at?: string | null
          id?: string
          max_uses?: number | null
          share_code: string
        }
        Update: {
          board_id?: string
          created_at?: string | null
          created_by?: string
          current_uses?: number | null
          expires_at?: string | null
          id?: string
          max_uses?: number | null
          share_code?: string
        }
        Relationships: [
          {
            foreignKeyName: "canvas_board_shares_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "canvas_boards"
            referencedColumns: ["id"]
          },
        ]
      }
      canvas_boards: {
        Row: {
          canvas_data: Json | null
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          is_published: boolean | null
          settings: Json | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          canvas_data?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          is_published?: boolean | null
          settings?: Json | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          canvas_data?: Json | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          is_published?: boolean | null
          settings?: Json | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      canvas_market_likes: {
        Row: {
          created_at: string | null
          id: string
          post_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "canvas_market_likes_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "canvas_market_posts"
            referencedColumns: ["id"]
          },
        ]
      }
      canvas_market_posts: {
        Row: {
          board_id: string
          created_at: string | null
          description: string | null
          featured: boolean | null
          id: string
          likes_count: number | null
          tags: string[] | null
          title: string
          updated_at: string | null
          views_count: number | null
        }
        Insert: {
          board_id: string
          created_at?: string | null
          description?: string | null
          featured?: boolean | null
          id?: string
          likes_count?: number | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          views_count?: number | null
        }
        Update: {
          board_id?: string
          created_at?: string | null
          description?: string | null
          featured?: boolean | null
          id?: string
          likes_count?: number | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "canvas_market_posts_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "canvas_boards"
            referencedColumns: ["id"]
          },
        ]
      }
      canvas_strokes: {
        Row: {
          board_id: string
          created_at: string | null
          id: string
          stroke_data: Json
          updated_at: string | null
          user_id: string
        }
        Insert: {
          board_id: string
          created_at?: string | null
          id?: string
          stroke_data: Json
          updated_at?: string | null
          user_id: string
        }
        Update: {
          board_id?: string
          created_at?: string | null
          id?: string
          stroke_data?: Json
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "canvas_strokes_board_id_fkey"
            columns: ["board_id"]
            isOneToOne: false
            referencedRelation: "canvas_boards"
            referencedColumns: ["id"]
          },
        ]
      }
      canvas_user_settings: {
        Row: {
          auto_save: boolean | null
          created_at: string | null
          default_brush_color: string | null
          default_brush_size: number | null
          default_canvas_size: Json | null
          id: string
          theme: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          auto_save?: boolean | null
          created_at?: string | null
          default_brush_color?: string | null
          default_brush_size?: number | null
          default_canvas_size?: Json | null
          id?: string
          theme?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          auto_save?: boolean | null
          created_at?: string | null
          default_brush_color?: string | null
          default_brush_size?: number | null
          default_canvas_size?: Json | null
          id?: string
          theme?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      chats: {
        Row: {
          created_at: string | null
          id: string
          participants: string[]
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          participants: string[]
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          participants?: string[]
          updated_at?: string | null
        }
        Relationships: []
      }
      cities: {
        Row: {
          area_km2: number | null
          bounds_northeast_lat: number | null
          bounds_northeast_lng: number | null
          bounds_southwest_lat: number | null
          bounds_southwest_lng: number | null
          created_at: string | null
          id: number
          latitude: number
          longitude: number
          name: string
          name_en: string | null
          population: number | null
          province: string | null
        }
        Insert: {
          area_km2?: number | null
          bounds_northeast_lat?: number | null
          bounds_northeast_lng?: number | null
          bounds_southwest_lat?: number | null
          bounds_southwest_lng?: number | null
          created_at?: string | null
          id?: number
          latitude: number
          longitude: number
          name: string
          name_en?: string | null
          population?: number | null
          province?: string | null
        }
        Update: {
          area_km2?: number | null
          bounds_northeast_lat?: number | null
          bounds_northeast_lng?: number | null
          bounds_southwest_lat?: number | null
          bounds_southwest_lng?: number | null
          created_at?: string | null
          id?: number
          latitude?: number
          longitude?: number
          name?: string
          name_en?: string | null
          population?: number | null
          province?: string | null
        }
        Relationships: []
      }
      comments: {
        Row: {
          content: string
          created_at: string | null
          id: string
          parent_id: string | null
          project_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          parent_id?: string | null
          project_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          parent_id?: string | null
          project_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_post_likes: {
        Row: {
          created_at: string | null
          id: string
          post_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          post_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          post_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_post_likes_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "community_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_post_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_post_replies: {
        Row: {
          content: string
          created_at: string | null
          id: string
          parent_id: string | null
          post_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          parent_id?: string | null
          post_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          parent_id?: string | null
          post_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "community_post_replies_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "community_post_replies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_post_replies_post_id_fkey"
            columns: ["post_id"]
            isOneToOne: false
            referencedRelation: "community_posts"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "community_post_replies_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      community_posts: {
        Row: {
          category: string
          content: string
          created_at: string | null
          id: string
          is_ai_generated: boolean | null
          likes_count: number | null
          replies_count: number | null
          tags: string[] | null
          title: string
          updated_at: string | null
          user_id: string
          views_count: number | null
        }
        Insert: {
          category: string
          content: string
          created_at?: string | null
          id?: string
          is_ai_generated?: boolean | null
          likes_count?: number | null
          replies_count?: number | null
          tags?: string[] | null
          title: string
          updated_at?: string | null
          user_id: string
          views_count?: number | null
        }
        Update: {
          category?: string
          content?: string
          created_at?: string | null
          id?: string
          is_ai_generated?: boolean | null
          likes_count?: number | null
          replies_count?: number | null
          tags?: string[] | null
          title?: string
          updated_at?: string | null
          user_id?: string
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "community_posts_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          content: string
          created_at: string | null
          id: string
          message_type: string
          metadata: Json | null
          project_id: string
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          message_type: string
          metadata?: Json | null
          project_id: string
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          message_type?: string
          metadata?: Json | null
          project_id?: string
          user_id?: string
        }
        Relationships: []
      }
      craft_ai_prompts: {
        Row: {
          content: string
          created_at: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      craft_note_blocks: {
        Row: {
          block_type: string
          content: string
          created_at: string
          id: string
          metadata: Json | null
          note_id: string
          position: number
          updated_at: string
          user_id: string
        }
        Insert: {
          block_type?: string
          content?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          note_id: string
          position?: number
          updated_at?: string
          user_id: string
        }
        Update: {
          block_type?: string
          content?: string
          created_at?: string
          id?: string
          metadata?: Json | null
          note_id?: string
          position?: number
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_note_blocks_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "craft_notes"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_note_tags: {
        Row: {
          created_at: string | null
          id: string
          note_id: string
          tag_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          note_id: string
          tag_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          note_id?: string
          tag_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_note_tags_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "craft_notes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "craft_note_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "craft_tags"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_note_topics: {
        Row: {
          created_at: string | null
          id: string
          note_id: string
          topic_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          note_id: string
          topic_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          note_id?: string
          topic_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_note_topics_note_id_fkey"
            columns: ["note_id"]
            isOneToOne: false
            referencedRelation: "craft_notes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "craft_note_topics_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "craft_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_notes: {
        Row: {
          content: string
          created_at: string | null
          id: string
          link_ai_summary: string | null
          link_date: string | null
          link_summary: string | null
          link_title: string | null
          link_url: string | null
          title: string
          type: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          link_ai_summary?: string | null
          link_date?: string | null
          link_summary?: string | null
          link_title?: string | null
          link_url?: string | null
          title: string
          type?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          link_ai_summary?: string | null
          link_date?: string | null
          link_summary?: string | null
          link_title?: string | null
          link_url?: string | null
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      craft_profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string
          full_name: string | null
          id: string
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          id: string
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          id?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      craft_rss_categories: {
        Row: {
          created_at: string | null
          id: string
          name: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: []
      }
      craft_rss_feeds: {
        Row: {
          category_id: string | null
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          last_fetched: string | null
          title: string
          updated_at: string | null
          url: string
          user_id: string
        }
        Insert: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_fetched?: string | null
          title: string
          updated_at?: string | null
          url: string
          user_id: string
        }
        Update: {
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_fetched?: string | null
          title?: string
          updated_at?: string | null
          url?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_rss_feeds_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "craft_rss_categories"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_rss_items: {
        Row: {
          author: string | null
          categories: string[] | null
          content: string | null
          created_at: string | null
          description: string | null
          feed_id: string
          feed_title: string
          guid: string
          id: string
          image_url: string | null
          link: string
          pub_date: string
          title: string
        }
        Insert: {
          author?: string | null
          categories?: string[] | null
          content?: string | null
          created_at?: string | null
          description?: string | null
          feed_id: string
          feed_title: string
          guid: string
          id?: string
          image_url?: string | null
          link: string
          pub_date: string
          title: string
        }
        Update: {
          author?: string | null
          categories?: string[] | null
          content?: string | null
          created_at?: string | null
          description?: string | null
          feed_id?: string
          feed_title?: string
          guid?: string
          id?: string
          image_url?: string | null
          link?: string
          pub_date?: string
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_rss_items_feed_id_fkey"
            columns: ["feed_id"]
            isOneToOne: false
            referencedRelation: "craft_rss_feeds"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_structured_documents: {
        Row: {
          blocks: Json
          created_at: string | null
          id: string
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          blocks?: Json
          created_at?: string | null
          id?: string
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          blocks?: Json
          created_at?: string | null
          id?: string
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      craft_tags: {
        Row: {
          color: string | null
          created_at: string | null
          id: string
          name: string
          user_id: string
        }
        Insert: {
          color?: string | null
          created_at?: string | null
          id?: string
          name: string
          user_id: string
        }
        Update: {
          color?: string | null
          created_at?: string | null
          id?: string
          name?: string
          user_id?: string
        }
        Relationships: []
      }
      craft_topic_rss_items: {
        Row: {
          created_at: string | null
          id: string
          rss_item_id: string
          topic_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          rss_item_id: string
          topic_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          rss_item_id?: string
          topic_id?: string
        }
        Relationships: []
      }
      craft_topic_tags: {
        Row: {
          created_at: string | null
          id: string
          tag_id: string
          topic_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          tag_id: string
          topic_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          tag_id?: string
          topic_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_topic_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "craft_tags"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "craft_topic_tags_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "craft_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      craft_topics: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          summary: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          summary?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          summary?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      craft_user_settings: {
        Row: {
          api_302_key: string | null
          created_at: string | null
          default_prompt_id: string | null
          default_qr_link: string | null
          default_share_template: Json | null
          id: string
          mowen_api_key: string | null
          rss2json_api_key: string | null
          theme: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          api_302_key?: string | null
          created_at?: string | null
          default_prompt_id?: string | null
          default_qr_link?: string | null
          default_share_template?: Json | null
          id?: string
          mowen_api_key?: string | null
          rss2json_api_key?: string | null
          theme?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          api_302_key?: string | null
          created_at?: string | null
          default_prompt_id?: string | null
          default_qr_link?: string | null
          default_share_template?: Json | null
          id?: string
          mowen_api_key?: string | null
          rss2json_api_key?: string | null
          theme?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "craft_user_settings_default_prompt_id_fkey"
            columns: ["default_prompt_id"]
            isOneToOne: false
            referencedRelation: "craft_ai_prompts"
            referencedColumns: ["id"]
          },
        ]
      }
      deployment_logs: {
        Row: {
          created_at: string | null
          id: string
          message: string
          project_id: string
          status: Database["public"]["Enums"]["deployment_status"]
        }
        Insert: {
          created_at?: string | null
          id?: string
          message: string
          project_id: string
          status: Database["public"]["Enums"]["deployment_status"]
        }
        Update: {
          created_at?: string | null
          id?: string
          message?: string
          project_id?: string
          status?: Database["public"]["Enums"]["deployment_status"]
        }
        Relationships: [
          {
            foreignKeyName: "deployment_logs_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
        ]
      }
      design_iterations: {
        Row: {
          created_at: string | null
          id: string
          image_url: string | null
          iteration_number: number | null
          project_id: string
          prompt: string
          style_parameters: Json | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          image_url?: string | null
          iteration_number?: number | null
          project_id: string
          prompt: string
          style_parameters?: Json | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          image_url?: string | null
          iteration_number?: number | null
          project_id?: string
          prompt?: string
          style_parameters?: Json | null
          user_id?: string
        }
        Relationships: []
      }
      design_projects: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          initial_prompt: string | null
          status: string | null
          title: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          initial_prompt?: string | null
          status?: string | null
          title: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          initial_prompt?: string | null
          status?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      favorites: {
        Row: {
          created_at: string | null
          id: string
          project_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          project_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          project_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "favorites_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "favorites_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      letter_routes: {
        Row: {
          best_time_to_ride: string | null
          city_id: number
          created_at: string | null
          difficulty_level: number | null
          distance_km: number | null
          estimated_time_minutes: number | null
          id: number
          letter: string
          route_features: string | null
          route_points: string
          safety_notes: string | null
          scenery_rating: number | null
          similarity_score: number | null
          updated_at: string | null
        }
        Insert: {
          best_time_to_ride?: string | null
          city_id: number
          created_at?: string | null
          difficulty_level?: number | null
          distance_km?: number | null
          estimated_time_minutes?: number | null
          id?: number
          letter: string
          route_features?: string | null
          route_points: string
          safety_notes?: string | null
          scenery_rating?: number | null
          similarity_score?: number | null
          updated_at?: string | null
        }
        Update: {
          best_time_to_ride?: string | null
          city_id?: number
          created_at?: string | null
          difficulty_level?: number | null
          distance_km?: number | null
          estimated_time_minutes?: number | null
          id?: number
          letter?: string
          route_features?: string | null
          route_points?: string
          safety_notes?: string | null
          scenery_rating?: number | null
          similarity_score?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      likes: {
        Row: {
          created_at: string | null
          id: string
          project_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          project_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          project_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "likes_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          chat_id: string
          content: string
          created_at: string | null
          id: string
          message_type: Database["public"]["Enums"]["message_type"] | null
          sender_id: string
        }
        Insert: {
          chat_id: string
          content: string
          created_at?: string | null
          id?: string
          message_type?: Database["public"]["Enums"]["message_type"] | null
          sender_id: string
        }
        Update: {
          chat_id?: string
          content?: string
          created_at?: string | null
          id?: string
          message_type?: Database["public"]["Enums"]["message_type"] | null
          sender_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_chat_id_fkey"
            columns: ["chat_id"]
            isOneToOne: false
            referencedRelation: "chats"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_sender_id_fkey"
            columns: ["sender_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          preferences: Json | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          preferences?: Json | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          preferences?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      project_favorites: {
        Row: {
          created_at: string | null
          id: string
          project_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          project_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          project_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_favorites_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_favorites_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      project_likes: {
        Row: {
          created_at: string | null
          id: string
          project_id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          project_id: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          project_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "project_likes_project_id_fkey"
            columns: ["project_id"]
            isOneToOne: false
            referencedRelation: "projects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "project_likes_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      projects: {
        Row: {
          category: string
          comments_count: number | null
          created_at: string | null
          demo_url: string | null
          deployment_status:
            | Database["public"]["Enums"]["deployment_status"]
            | null
          deployment_url: string | null
          description: string | null
          file_path: string | null
          id: string
          is_public: boolean | null
          likes_count: number | null
          slug: string
          source_content: string | null
          source_type: Database["public"]["Enums"]["source_type"]
          subdomain: string | null
          tags: string[] | null
          thumbnail_url: string | null
          title: string
          updated_at: string | null
          user_id: string
          views_count: number | null
        }
        Insert: {
          category: string
          comments_count?: number | null
          created_at?: string | null
          demo_url?: string | null
          deployment_status?:
            | Database["public"]["Enums"]["deployment_status"]
            | null
          deployment_url?: string | null
          description?: string | null
          file_path?: string | null
          id?: string
          is_public?: boolean | null
          likes_count?: number | null
          slug: string
          source_content?: string | null
          source_type: Database["public"]["Enums"]["source_type"]
          subdomain?: string | null
          tags?: string[] | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
          user_id: string
          views_count?: number | null
        }
        Update: {
          category?: string
          comments_count?: number | null
          created_at?: string | null
          demo_url?: string | null
          deployment_status?:
            | Database["public"]["Enums"]["deployment_status"]
            | null
          deployment_url?: string | null
          description?: string | null
          file_path?: string | null
          id?: string
          is_public?: boolean | null
          likes_count?: number | null
          slug?: string
          source_content?: string | null
          source_type?: Database["public"]["Enums"]["source_type"]
          subdomain?: string | null
          tags?: string[] | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
          user_id?: string
          views_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "projects_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      render_results: {
        Row: {
          created_at: string | null
          id: string
          image_url: string | null
          iteration_id: string
          parameters: Json | null
          project_id: string
          render_type: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          image_url?: string | null
          iteration_id: string
          parameters?: Json | null
          project_id: string
          render_type?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          image_url?: string | null
          iteration_id?: string
          parameters?: Json | null
          project_id?: string
          render_type?: string | null
          user_id?: string
        }
        Relationships: []
      }
      route_ratings: {
        Row: {
          comment: string | null
          created_at: string | null
          id: number
          photo_urls: string[] | null
          rating: number | null
          riding_date: string | null
          route_id: number
          user_id: string | null
          weather_conditions: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          id?: number
          photo_urls?: string[] | null
          rating?: number | null
          riding_date?: string | null
          route_id: number
          user_id?: string | null
          weather_conditions?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          id?: number
          photo_urls?: string[] | null
          rating?: number | null
          riding_date?: string | null
          route_id?: number
          user_id?: string | null
          weather_conditions?: string | null
        }
        Relationships: []
      }
      user_saved_routes: {
        Row: {
          city_id: number
          completed_at: string | null
          custom_name: string | null
          id: number
          is_completed: boolean | null
          letter: string
          notes: string | null
          route_id: number | null
          saved_at: string | null
          user_id: string | null
        }
        Insert: {
          city_id: number
          completed_at?: string | null
          custom_name?: string | null
          id?: number
          is_completed?: boolean | null
          letter: string
          notes?: string | null
          route_id?: number | null
          saved_at?: string | null
          user_id?: string | null
        }
        Update: {
          city_id?: number
          completed_at?: string | null
          custom_name?: string | null
          id?: number
          is_completed?: boolean | null
          letter?: string
          notes?: string | null
          route_id?: number | null
          saved_at?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          email: string
          full_name: string | null
          github_url: string | null
          id: string
          linkedin_url: string | null
          location: string | null
          twitter_url: string | null
          updated_at: string | null
          username: string
          website: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email: string
          full_name?: string | null
          github_url?: string | null
          id: string
          linkedin_url?: string | null
          location?: string | null
          twitter_url?: string | null
          updated_at?: string | null
          username: string
          website?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          email?: string
          full_name?: string | null
          github_url?: string | null
          id?: string
          linkedin_url?: string | null
          location?: string | null
          twitter_url?: string | null
          updated_at?: string | null
          username?: string
          website?: string | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_board_access: {
        Args: { board_uuid: string; user_uuid: string }
        Returns: boolean
      }
      user_owns_board: {
        Args: { board_uuid: string; user_uuid: string }
        Returns: boolean
      }
    }
    Enums: {
      deployment_status: "pending" | "processing" | "success" | "failed"
      message_type: "text" | "image" | "file"
      source_type: "html" | "zip"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      deployment_status: ["pending", "processing", "success", "failed"],
      message_type: ["text", "image", "file"],
      source_type: ["html", "zip"],
    },
  },
} as const
