// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://zhjrfpuoiblhbstcpkcz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpoanJmcHVvaWJsaGJzdGNwa2N6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUyMTUzNzgsImV4cCI6MjA1MDc5MTM3OH0.1JLz_RZUdWGfWEK69ckq6FB9ywHuwUnem7NdFGjnuks";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});