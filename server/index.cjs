// 基于 Node.js + socket.io 的多人实时协作白板服务端
// 启动：node server/index.js

const http = require('http');
const { Server } = require('socket.io');

const PORT = 4000;

const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*', // 开发阶段允许所有来源，生产环境请配置白名单
    methods: ['GET', 'POST']
  }
});

// 记录每个房间的最新画布操作（可选，实际同步以客户端为准）
const boardOpsCache = {};

// 记录每个房间的最后广播时间，用于控制日志频率
const lastBroadcastTime = {};

io.on('connection', (socket) => {
  console.log('[server] 新连接:', socket.id);

  // 加入画板房间
  socket.on('join-board', (boardId) => {
    socket.join(boardId);
    const room = io.sockets.adapter.rooms.get(boardId);
    console.log(`[server] ${socket.id} 加入房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
    // 可选：新用户加入后，推送当前房间最新画布状态
    if (boardOpsCache[boardId]) {
      socket.emit('board-ops-sync', boardOpsCache[boardId]);
    }
  });

  // 收到用户的画布操作，转发给同房间其他用户
  socket.on('board-op', ({ boardId, op }) => {
    boardOpsCache[boardId] = op;
    const room = io.sockets.adapter.rooms.get(boardId);

    // 控制日志频率，每5秒最多输出一次广播日志
    const now = Date.now();
    const lastTime = lastBroadcastTime[boardId] || 0;
    if (now - lastTime > 5000) { // 5秒间隔
      console.log(`[server] ${socket.id} 广播 board-op 到房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
      lastBroadcastTime[boardId] = now;
    }

    socket.to(boardId).emit('board-op', op);
  });

  // 离开房间
  socket.on('leave-board', (boardId) => {
    socket.leave(boardId);
    const room = io.sockets.adapter.rooms.get(boardId);
    console.log(`[server] ${socket.id} 离开房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
    // 清理该房间的广播时间记录
    delete lastBroadcastTime[boardId];
  });

  // 断开连接自动离开所有房间
  socket.on('disconnect', () => {
    console.log(`[server] ${socket.id} 断开连接`);
  });
});

server.listen(PORT, () => {
  console.log(`Socket.io 协作服务已启动，端口: ${PORT}`);
});
