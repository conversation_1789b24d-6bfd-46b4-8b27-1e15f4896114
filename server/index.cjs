// 基于 Node.js + socket.io 的多人实时协作白板服务端
// 启动：node server/index.js

const http = require('http');
const { Server } = require('socket.io');

const PORT = 4000;

const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: '*', // 开发阶段允许所有来源，生产环境请配置白名单
    methods: ['GET', 'POST']
  }
});

// 记录每个房间的完整画布状态（用于新用户同步）
const boardStateCache = {};

// 记录每个房间的最后广播时间，用于控制日志频率
const lastBroadcastTime = {};

io.on('connection', (socket) => {
  console.log('[server] 新连接:', socket.id);

  // 加入画板房间
  socket.on('join-board', (boardId) => {
    socket.join(boardId);
    const room = io.sockets.adapter.rooms.get(boardId);
    console.log(`[server] ${socket.id} 加入房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
    // 新用户加入后，推送当前房间完整画布状态
    if (boardStateCache[boardId]) {
      socket.emit('board-ops-sync', boardStateCache[boardId]);
    }
  });

  // 收到用户的画布操作，转发给同房间其他用户
  socket.on('board-op', ({ boardId, canvasState }) => {
    const room = io.sockets.adapter.rooms.get(boardId);

    // 控制日志频率，每5秒最多输出一次广播日志
    const now = Date.now();
    const lastTime = lastBroadcastTime[boardId] || 0;
    if (now - lastTime > 5000) { // 5秒间隔
      console.log(`[server] ${socket.id} 广播画布状态到房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
      lastBroadcastTime[boardId] = now;
    }

    // 转发画布状态给其他用户
    socket.to(boardId).emit('board-op', canvasState);
  });

  // 接收完整画布状态更新（用于缓存）
  socket.on('board-state-update', ({ boardId, canvasState }) => {
    boardStateCache[boardId] = canvasState;
    console.log(`[server] 更新房间 ${boardId} 的画布状态缓存`);
  });

  // 离开房间
  socket.on('leave-board', (boardId) => {
    socket.leave(boardId);
    const room = io.sockets.adapter.rooms.get(boardId);
    console.log(`[server] ${socket.id} 离开房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
    // 清理该房间的广播时间记录
    delete lastBroadcastTime[boardId];
  });

  // 断开连接自动离开所有房间
  socket.on('disconnect', () => {
    console.log(`[server] ${socket.id} 断开连接`);
  });
});

server.listen(PORT, () => {
  console.log(`Socket.io 协作服务已启动，端口: ${PORT}`);
});
