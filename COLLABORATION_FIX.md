# 协作画板问题修复

## 问题描述
1. **日志刷屏**: 当用户在协作画板上绘制线条时，服务器后台日志和浏览器控制台会出现大量重复的日志输出
2. **内容丢失**: 画板上画了一笔后之前的内容消失，继续画时会闪烁显示之前的内容然后又消失

## 问题原因
1. **频繁的事件触发**: 在绘制模式下，Fabric.js 的 `object:added` 事件会在绘制过程中被频繁触发
2. **不当的同步机制**: 每次收到远程更新时都清空整个画布然后重新加载，导致本地内容丢失
3. **时序问题**: 远程更新和本地绘制之间存在竞态条件，导致内容覆盖

## 修复方案

### 1. 事件监听优化

#### 问题分析
- 原来监听 `object:added` 事件，在绘制过程中会被频繁触发
- 每次触发都会广播完整的画布状态，造成性能问题和日志刷屏

#### 解决方案
- 使用 `path:created` 事件监听绘制完成，而不是绘制过程
- 只在非绘制模式下响应 `object:added` 事件（用于形状添加）
- 增加防抖机制，200ms 内的多次变更只广播一次

```javascript
// 监听绘制完成事件
fabricCanvas.on("path:created", onPathCreated);

// 只在非绘制模式下监听对象添加
fabricCanvas.on("object:added", () => {
  if (!fabricCanvas.isDrawingMode && !isRemoteUpdate.current) {
    debouncedBroadcast();
  }
});
```

### 2. 同步机制优化

#### 问题分析
- 原来每次收到远程更新都清空画布重新加载，导致本地内容丢失
- 远程更新和本地绘制存在竞态条件

#### 解决方案
- 添加绘制状态检查，避免在用户正在绘制时应用远程更新
- 简化同步逻辑，使用全量同步但增加保护机制

```javascript
socket.on("board-op", (canvasState) => {
  if (fabricCanvas && canvasState) {
    isRemoteUpdate.current = true;

    // 只在没有本地绘制时更新
    if (!fabricCanvas.isDrawingMode || fabricCanvas.getObjects().length === 0) {
      fabricCanvas.loadFromJSON(canvasState, () => {
        fabricCanvas.renderAll();
        isRemoteUpdate.current = false;
      });
    } else {
      isRemoteUpdate.current = false;
    }
  }
});
```

### 2. 服务器端优化 (server/index.cjs)

#### 日志频率控制
- 添加了广播日志的时间间隔控制，每5秒最多输出一次广播日志
- 避免了连续的重复日志输出

#### 具体改动
```javascript
// 控制日志频率，每5秒最多输出一次广播日志
const now = Date.now();
const lastTime = lastBroadcastTime[boardId] || 0;
if (now - lastTime > 5000) { // 5秒间隔
  console.log(`[server] ${socket.id} 广播 board-op 到房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
  lastBroadcastTime[boardId] = now;
}
```

#### 资源清理
- 在用户离开房间时清理广播时间记录
- 添加了断开连接的日志记录

## 修复效果

### 修复前
- 绘制一条线时会产生数十条重复的日志
- 服务器和客户端控制台被大量日志刷屏
- 影响调试和用户体验

### 修复后
- 绘制完成后只产生一条广播日志
- 服务器日志频率被控制在合理范围内
- 保持了协作功能的实时性，同时大大减少了日志噪音

## 技术要点

1. **Fabric.js 事件机制**: 理解不同事件的触发时机，选择合适的事件类型
2. **防抖技术**: 使用 setTimeout 和 clearTimeout 实现防抖，避免频繁操作
3. **条件判断**: 通过 `isDrawingMode` 和 `isRemoteUpdate` 标志位避免不必要的事件处理
4. **日志管理**: 在保持调试信息的同时，控制日志输出频率

## 测试建议

1. 打开两个浏览器窗口访问同一个画板
2. 在一个窗口中绘制线条
3. 观察服务器日志和浏览器控制台，确认日志输出频率正常
4. 验证另一个窗口能正常接收到绘制内容的同步
