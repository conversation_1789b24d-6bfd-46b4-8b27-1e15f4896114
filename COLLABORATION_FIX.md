# 协作画板日志刷屏问题修复

## 问题描述
当用户在协作画板上绘制线条时，服务器后台日志和浏览器控制台会出现大量重复的日志输出，导致日志刷屏。

## 问题原因
1. **频繁的事件触发**: 在绘制模式下，Fabric.js 的 `object:added` 事件会在绘制过程中被频繁触发，每个笔画点都可能触发一次事件
2. **实时广播**: 每次事件触发都会导致整个画布状态被序列化并通过 WebSocket 广播给其他用户
3. **详细日志输出**: 服务器和客户端都输出了详细的调试信息，包括完整的画布数据

## 修复方案

### 1. 客户端优化 (src/components/canvas/CanvasEditor.tsx)

#### 事件监听优化
- **替换事件类型**: 使用 `path:created` 事件替代 `object:added` 来监听绘制完成，而不是绘制过程中的每个点
- **条件监听**: 只在非绘制模式下响应 `object:added` 事件，避免绘制过程中的频繁触发
- **防抖机制**: 添加 100ms 的防抖延迟，避免短时间内的重复广播

#### 具体改动
```javascript
// 原来的实现
fabricCanvas.on("object:added", onCanvasChange);

// 修复后的实现
fabricCanvas.on("path:created", onPathCreated); // 绘制路径完成
fabricCanvas.on("object:added", (e) => {
  // 只有在非绘制模式下才响应，避免绘制过程中的频繁触发
  if (!fabricCanvas.isDrawingMode && !isRemoteUpdate.current) {
    onObjectChange();
  }
});

// 添加防抖机制
const debouncedBroadcast = () => {
  if (broadcastTimeout) {
    clearTimeout(broadcastTimeout);
  }
  broadcastTimeout = setTimeout(() => {
    // 广播逻辑
  }, 100);
};
```

#### 日志优化
- 移除了详细的画布数据输出，只保留关键的状态信息
- 减少了控制台日志的冗余信息

### 2. 服务器端优化 (server/index.cjs)

#### 日志频率控制
- 添加了广播日志的时间间隔控制，每5秒最多输出一次广播日志
- 避免了连续的重复日志输出

#### 具体改动
```javascript
// 控制日志频率，每5秒最多输出一次广播日志
const now = Date.now();
const lastTime = lastBroadcastTime[boardId] || 0;
if (now - lastTime > 5000) { // 5秒间隔
  console.log(`[server] ${socket.id} 广播 board-op 到房间 ${boardId}，当前房间人数: ${room ? room.size : 0}`);
  lastBroadcastTime[boardId] = now;
}
```

#### 资源清理
- 在用户离开房间时清理广播时间记录
- 添加了断开连接的日志记录

## 修复效果

### 修复前
- 绘制一条线时会产生数十条重复的日志
- 服务器和客户端控制台被大量日志刷屏
- 影响调试和用户体验

### 修复后
- 绘制完成后只产生一条广播日志
- 服务器日志频率被控制在合理范围内
- 保持了协作功能的实时性，同时大大减少了日志噪音

## 技术要点

1. **Fabric.js 事件机制**: 理解不同事件的触发时机，选择合适的事件类型
2. **防抖技术**: 使用 setTimeout 和 clearTimeout 实现防抖，避免频繁操作
3. **条件判断**: 通过 `isDrawingMode` 和 `isRemoteUpdate` 标志位避免不必要的事件处理
4. **日志管理**: 在保持调试信息的同时，控制日志输出频率

## 测试建议

1. 打开两个浏览器窗口访问同一个画板
2. 在一个窗口中绘制线条
3. 观察服务器日志和浏览器控制台，确认日志输出频率正常
4. 验证另一个窗口能正常接收到绘制内容的同步
