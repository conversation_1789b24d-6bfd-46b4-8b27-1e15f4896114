-- Drop all policies on canvas_boards to start fresh
DROP POLICY IF EXISTS "Users can view boards they have access to" ON canvas_boards;
DROP POLICY IF EXISTS "Users can create their own boards" ON canvas_boards;
DROP POLICY IF EXISTS "Users can update boards they own" ON canvas_boards;
DROP POLICY IF EXISTS "Users can delete boards they own" ON canvas_boards;

-- Create simple, non-recursive policies
CREATE POLICY "Users can create their own boards" 
ON canvas_boards 
FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own boards" 
ON canvas_boards 
FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own boards" 
ON canvas_boards 
FOR DELETE 
USING (auth.uid() = user_id);

-- Simple SELECT policy that doesn't reference other tables for now
CREATE POLICY "Users can view their own and public boards" 
ON canvas_boards 
FOR SELECT 
USING (auth.uid() = user_id OR is_public = true);