-- RLS Policies for canvas_boards
CREATE POLICY "Users can create their own boards" ON public.canvas_boards
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view boards they own or collaborate on" ON public.canvas_boards
FOR SELECT USING (
  auth.uid() = user_id OR 
  EXISTS (
    SELECT 1 FROM public.canvas_board_collaborators 
    WHERE board_id = canvas_boards.id AND user_id = auth.uid()
  ) OR
  is_public = true
);

CREATE POLICY "Users can update boards they own" ON public.canvas_boards
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete boards they own" ON public.canvas_boards
FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for canvas_board_collaborators
CREATE POLICY "Board owners can manage collaborators" ON public.canvas_board_collaborators
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_collaborators.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can view collaborators of boards they have access to" ON public.canvas_board_collaborators
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_collaborators.board_id AND 
    (user_id = auth.uid() OR is_public = true)
  ) OR
  EXISTS (
    SELECT 1 FROM public.canvas_board_collaborators c2
    WHERE c2.board_id = canvas_board_collaborators.board_id AND c2.user_id = auth.uid()
  )
);

-- RLS Policies for canvas_strokes
CREATE POLICY "Users can create strokes on boards they have access to" ON public.canvas_strokes
FOR INSERT WITH CHECK (
  auth.uid() = user_id AND
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_strokes.board_id AND 
    (user_id = auth.uid() OR 
     EXISTS (
       SELECT 1 FROM public.canvas_board_collaborators 
       WHERE board_id = canvas_strokes.board_id AND user_id = auth.uid()
     ))
  )
);

CREATE POLICY "Users can view strokes of boards they have access to" ON public.canvas_strokes
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_strokes.board_id AND 
    (user_id = auth.uid() OR is_public = true OR
     EXISTS (
       SELECT 1 FROM public.canvas_board_collaborators 
       WHERE board_id = canvas_strokes.board_id AND user_id = auth.uid()
     ))
  )
);

-- RLS Policies for canvas_board_shares
CREATE POLICY "Users can create shares for their boards" ON public.canvas_board_shares
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_shares.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can view shares of their boards" ON public.canvas_board_shares
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_shares.board_id AND user_id = auth.uid()
  )
);

-- RLS Policies for canvas_market_posts
CREATE POLICY "Anyone can view market posts" ON public.canvas_market_posts
FOR SELECT USING (true);

CREATE POLICY "Users can create market posts for their boards" ON public.canvas_market_posts
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_market_posts.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their market posts" ON public.canvas_market_posts
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_market_posts.board_id AND user_id = auth.uid()
  )
);

-- RLS Policies for canvas_market_likes
CREATE POLICY "Users can like market posts" ON public.canvas_market_likes
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all market likes" ON public.canvas_market_likes
FOR SELECT USING (true);

CREATE POLICY "Users can remove their own likes" ON public.canvas_market_likes
FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for canvas_user_settings
CREATE POLICY "Users can manage their own settings" ON public.canvas_user_settings
FOR ALL USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_canvas_boards_user_id ON public.canvas_boards(user_id);
CREATE INDEX idx_canvas_boards_is_public ON public.canvas_boards(is_public);
CREATE INDEX idx_canvas_board_collaborators_board_id ON public.canvas_board_collaborators(board_id);
CREATE INDEX idx_canvas_board_collaborators_user_id ON public.canvas_board_collaborators(user_id);
CREATE INDEX idx_canvas_strokes_board_id ON public.canvas_strokes(board_id);
CREATE INDEX idx_canvas_strokes_created_at ON public.canvas_strokes(created_at);
CREATE INDEX idx_canvas_market_posts_created_at ON public.canvas_market_posts(created_at);
CREATE INDEX idx_canvas_market_posts_likes_count ON public.canvas_market_posts(likes_count);

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_strokes;
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_board_collaborators;
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_boards;