-- Create canvas boards table
CREATE TABLE public.canvas_boards (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  is_published B<PERSON><PERSON><PERSON><PERSON> DEFAULT false,
  thumbnail_url TEXT,
  canvas_data JSONB DEFAULT '{"strokes": [], "settings": {}}'::jsonb,
  settings JSONB DEFAULT '{"width": 800, "height": 600, "backgroundColor": "#ffffff"}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas board collaborators table
CREATE TABLE public.canvas_board_collaborators (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  role TEXT NOT NULL DEFAULT 'editor',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(board_id, user_id)
);

-- Create canvas strokes table for real-time collaboration
CREATE TABLE public.canvas_strokes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  stroke_data JSONB NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas board shares table
CREATE TABLE public.canvas_board_shares (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  share_code TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas market posts table
CREATE TABLE public.canvas_market_posts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  tags TEXT[] DEFAULT '{}',
  likes_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas market likes table
CREATE TABLE public.canvas_market_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.canvas_market_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(post_id, user_id)
);

-- Create canvas user settings table
CREATE TABLE public.canvas_user_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE,
  default_brush_color TEXT DEFAULT '#000000',
  default_brush_size INTEGER DEFAULT 5,
  default_canvas_size JSONB DEFAULT '{"width": 800, "height": 600}'::jsonb,
  auto_save BOOLEAN DEFAULT true,
  theme TEXT DEFAULT 'light',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.canvas_boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_board_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_strokes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_board_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_market_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_market_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_user_settings ENABLE ROW LEVEL SECURITY;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_canvas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_canvas_boards_updated_at
  BEFORE UPDATE ON public.canvas_boards
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_strokes_updated_at
  BEFORE UPDATE ON public.canvas_strokes
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_market_posts_updated_at
  BEFORE UPDATE ON public.canvas_market_posts
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_user_settings_updated_at
  BEFORE UPDATE ON public.canvas_user_settings
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

-- Enable realtime for real-time collaboration
ALTER TABLE public.canvas_strokes REPLICA IDENTITY FULL;
ALTER TABLE public.canvas_board_collaborators REPLICA IDENTITY FULL;
ALTER TABLE public.canvas_boards REPLICA IDENTITY FULL;