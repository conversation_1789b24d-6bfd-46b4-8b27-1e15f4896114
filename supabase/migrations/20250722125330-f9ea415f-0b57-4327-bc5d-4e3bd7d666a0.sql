-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view boards they own or collaborate on" ON canvas_boards;
DROP POLICY IF EXISTS "Users can view collaborators of boards they have access to" ON canvas_board_collaborators;

-- Create security definer functions to avoid recursive policy issues
CREATE OR REPLACE FUNCTION get_user_board_access(board_uuid uuid, user_uuid uuid)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if user owns the board or is a collaborator or board is public
  RETURN EXISTS (
    SELECT 1 FROM canvas_boards 
    WHERE id = board_uuid AND (
      user_id = user_uuid OR 
      is_public = true
    )
  ) OR EXISTS (
    SELECT 1 FROM canvas_board_collaborators 
    WHERE board_id = board_uuid AND user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

CREATE OR REPLACE FUNCTION user_owns_board(board_uuid uuid, user_uuid uuid)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM canvas_boards 
    WHERE id = board_uuid AND user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Recreate policies using security definer functions
CREATE POLICY "Users can view boards they have access to" 
ON canvas_boards 
FOR SELECT 
USING (
  auth.uid() = user_id OR 
  is_public = true OR 
  EXISTS (
    SELECT 1 FROM canvas_board_collaborators 
    WHERE board_id = canvas_boards.id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can view collaborators of accessible boards" 
ON canvas_board_collaborators 
FOR SELECT 
USING (get_user_board_access(board_id, auth.uid()));

-- Enable RLS on tables that don't have it yet
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE render_results ENABLE ROW LEVEL SECURITY;
ALTER TABLE deployment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE design_iterations ENABLE ROW LEVEL SECURITY;
ALTER TABLE design_projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE cities ENABLE ROW LEVEL SECURITY;
ALTER TABLE letter_routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_saved_routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE route_ratings ENABLE ROW LEVEL SECURITY;

-- Add basic policies for tables without them
CREATE POLICY "Users can view public projects" ON projects FOR SELECT USING (is_public = true OR user_id = auth.uid());
CREATE POLICY "Users can manage own projects" ON projects FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Enable read access for render results" ON render_results FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Enable insert access for render results" ON render_results FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Enable read access for design iterations" ON design_iterations FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Enable insert access for design iterations" ON design_iterations FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Enable read access for design projects" ON design_projects FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Enable all access for design projects" ON design_projects FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Enable read access for conversations" ON conversations FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Enable insert access for conversations" ON conversations FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Enable read access for cities" ON cities FOR SELECT USING (true);
CREATE POLICY "Enable read access for letter routes" ON letter_routes FOR SELECT USING (true);

CREATE POLICY "Enable access for user saved routes" ON user_saved_routes FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Enable access for route ratings" ON route_ratings FOR ALL USING (user_id = auth.uid());