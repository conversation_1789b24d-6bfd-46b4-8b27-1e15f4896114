-- Create canvas boards table
CREATE TABLE public.canvas_boards (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  title TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT false,
  is_published BO<PERSON>EAN DEFAULT false,
  thumbnail_url TEXT,
  canvas_data JSONB DEFAULT '{"strokes": [], "settings": {}}'::jsonb,
  settings JSONB DEFAULT '{"width": 800, "height": 600, "backgroundColor": "#ffffff"}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas board collaborators table
CREATE TABLE public.canvas_board_collaborators (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  role TEXT NOT NULL DEFAULT 'editor', -- 'owner', 'editor', 'viewer'
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  last_active TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(board_id, user_id)
);

-- Create canvas strokes table for real-time collaboration
CREATE TABLE public.canvas_strokes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  stroke_data JSONB NOT NULL, -- Contains path, color, width, type etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas board shares table
CREATE TABLE public.canvas_board_shares (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  share_code TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  max_uses INTEGER,
  current_uses INTEGER DEFAULT 0,
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas market posts table
CREATE TABLE public.canvas_market_posts (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  board_id UUID NOT NULL REFERENCES public.canvas_boards(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  tags TEXT[] DEFAULT '{}',
  likes_count INTEGER DEFAULT 0,
  views_count INTEGER DEFAULT 0,
  featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create canvas market likes table
CREATE TABLE public.canvas_market_likes (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  post_id UUID NOT NULL REFERENCES public.canvas_market_posts(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  UNIQUE(post_id, user_id)
);

-- Create canvas user settings table
CREATE TABLE public.canvas_user_settings (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL UNIQUE,
  default_brush_color TEXT DEFAULT '#000000',
  default_brush_size INTEGER DEFAULT 5,
  default_canvas_size JSONB DEFAULT '{"width": 800, "height": 600}'::jsonb,
  auto_save BOOLEAN DEFAULT true,
  theme TEXT DEFAULT 'light',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.canvas_boards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_board_collaborators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_strokes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_board_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_market_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_market_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.canvas_user_settings ENABLE ROW LEVEL SECURITY;

-- RLS Policies for canvas_boards
CREATE POLICY "Users can create their own boards" ON public.canvas_boards
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view boards they own or collaborate on" ON public.canvas_boards
FOR SELECT USING (
  auth.uid() = user_id OR 
  EXISTS (
    SELECT 1 FROM public.canvas_board_collaborators 
    WHERE board_id = canvas_boards.id AND user_id = auth.uid()
  ) OR
  is_public = true
);

CREATE POLICY "Users can update boards they own" ON public.canvas_boards
FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete boards they own" ON public.canvas_boards
FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for canvas_board_collaborators
CREATE POLICY "Board owners can manage collaborators" ON public.canvas_board_collaborators
FOR ALL USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_collaborators.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can view collaborators of boards they have access to" ON public.canvas_board_collaborators
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_collaborators.board_id AND 
    (user_id = auth.uid() OR is_public = true)
  ) OR
  EXISTS (
    SELECT 1 FROM public.canvas_board_collaborators c2
    WHERE c2.board_id = canvas_board_collaborators.board_id AND c2.user_id = auth.uid()
  )
);

-- RLS Policies for canvas_strokes
CREATE POLICY "Users can create strokes on boards they have access to" ON public.canvas_strokes
FOR INSERT WITH CHECK (
  auth.uid() = user_id AND
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_strokes.board_id AND 
    (user_id = auth.uid() OR 
     EXISTS (
       SELECT 1 FROM public.canvas_board_collaborators 
       WHERE board_id = canvas_strokes.board_id AND user_id = auth.uid()
     ))
  )
);

CREATE POLICY "Users can view strokes of boards they have access to" ON public.canvas_strokes
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_strokes.board_id AND 
    (user_id = auth.uid() OR is_public = true OR
     EXISTS (
       SELECT 1 FROM public.canvas_board_collaborators 
       WHERE board_id = canvas_strokes.board_id AND user_id = auth.uid()
     ))
  )
);

-- RLS Policies for canvas_board_shares
CREATE POLICY "Users can create shares for their boards" ON public.canvas_board_shares
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_shares.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can view shares of their boards" ON public.canvas_board_shares
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_board_shares.board_id AND user_id = auth.uid()
  )
);

-- RLS Policies for canvas_market_posts
CREATE POLICY "Anyone can view market posts" ON public.canvas_market_posts
FOR SELECT USING (true);

CREATE POLICY "Users can create market posts for their boards" ON public.canvas_market_posts
FOR INSERT WITH CHECK (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_market_posts.board_id AND user_id = auth.uid()
  )
);

CREATE POLICY "Users can update their market posts" ON public.canvas_market_posts
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM public.canvas_boards 
    WHERE id = canvas_market_posts.board_id AND user_id = auth.uid()
  )
);

-- RLS Policies for canvas_market_likes
CREATE POLICY "Users can like market posts" ON public.canvas_market_likes
FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view all market likes" ON public.canvas_market_likes
FOR SELECT USING (true);

CREATE POLICY "Users can remove their own likes" ON public.canvas_market_likes
FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for canvas_user_settings
CREATE POLICY "Users can manage their own settings" ON public.canvas_user_settings
FOR ALL USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX idx_canvas_boards_user_id ON public.canvas_boards(user_id);
CREATE INDEX idx_canvas_boards_is_public ON public.canvas_boards(is_public);
CREATE INDEX idx_canvas_board_collaborators_board_id ON public.canvas_board_collaborators(board_id);
CREATE INDEX idx_canvas_board_collaborators_user_id ON public.canvas_board_collaborators(user_id);
CREATE INDEX idx_canvas_strokes_board_id ON public.canvas_strokes(board_id);
CREATE INDEX idx_canvas_strokes_created_at ON public.canvas_strokes(created_at);
CREATE INDEX idx_canvas_market_posts_created_at ON public.canvas_market_posts(created_at);
CREATE INDEX idx_canvas_market_posts_likes_count ON public.canvas_market_posts(likes_count);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_canvas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_canvas_boards_updated_at
  BEFORE UPDATE ON public.canvas_boards
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_strokes_updated_at
  BEFORE UPDATE ON public.canvas_strokes
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_market_posts_updated_at
  BEFORE UPDATE ON public.canvas_market_posts
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_updated_at();

CREATE TRIGGER update_canvas_user_settings_updated_at
  BEFORE UPDATE ON public.canvas_user_settings
  FOR EACH ROW EXECUTE FUNCTION public.update_canvas_user_settings_updated_at();

-- Enable realtime for real-time collaboration
ALTER TABLE public.canvas_strokes REPLICA IDENTITY FULL;
ALTER TABLE public.canvas_board_collaborators REPLICA IDENTITY FULL;
ALTER TABLE public.canvas_boards REPLICA IDENTITY FULL;

-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_strokes;
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_board_collaborators;
ALTER PUBLICATION supabase_realtime ADD TABLE public.canvas_boards;